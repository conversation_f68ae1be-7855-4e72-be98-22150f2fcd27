// Custom styles for the Ops Team component

// Card title styling
.card-title {
  color: #3F828B;
}

// Shareholder card styling
.shareholder-card {
  border: 1px solid #e3e6f0;
  border-radius: 8px;
  margin-bottom: 1rem;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border-color: #d1d3e2;
  }

  .card-header {
    background-color: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
    padding: 0.75rem 1rem;

    h6 {
      color: #5a5c69;
      font-weight: 600;
      margin: 0;
    }
  }

  .card-body {
    padding: 1.25rem;
  }
}

// Form styling improvements
.form-label {
  font-weight: 500;
  color: #5a5c69;
  margin-bottom: 0.5rem;

  .text-danger {
    color: #e74a3b !important;
  }
}



.btn-outline-danger {
  color: #e74a3b;
  border-color: #e74a3b;

  &:hover {
    background-color: #e74a3b;
    border-color: #e74a3b;
    color: #fff;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}



// Modern table styling


.modern-table {
  border-collapse: separate;
  border-spacing: 0;
  width: 100%;

  th {
    position: sticky;
    top: 0;
    background-color: #f8f9fa;
    font-weight: 600;
    color: #495057;
    padding: 12px 16px;
    text-align: left;
    border-bottom: 2px solid #dee2e6;
    white-space: nowrap;

    &[sortable] {
      cursor: pointer;
      position: relative;
      padding-right: 24px;

      &:after {
        content: '';
        position: absolute;
        right: 8px;
        top: 50%;
        transform: translateY(-50%);
        width: 0;
        height: 0;
        border-left: 5px solid transparent;
        border-right: 5px solid transparent;
        border-bottom: 5px solid #adb5bd;
        opacity: 0.3;
      }

      &.asc:after {
        opacity: 1;
        border-bottom: 5px solid #495057;
      }

      &.desc:after {
        opacity: 1;
        transform: translateY(-50%) rotate(180deg);
        border-bottom: 5px solid #495057;
      }
    }
  }

  td {
    padding: 12px 16px;
    vertical-align: middle;
    border-bottom: 1px solid #e9ecef;
    white-space: nowrap;
  }

  tbody tr {
    transition: background-color 0.2s;

    &:hover {
      background-color: rgba(0, 0, 0, 0.02);
    }
  }


}

// Badge styling
.badge {
  padding: 0.4em 0.8em;
  font-weight: 500;
  font-size: 0.75rem;

  &.rounded-pill {
    padding-left: 0.8em;
    padding-right: 0.8em;
  }

  &.bg-light-primary {
    background-color: rgba(63, 130, 139, 0.1);
    font-family: monospace;
    font-weight: 600;
    letter-spacing: 0.5px;
  }
}



// Hide spinners on number input fields
input[type=number]::-webkit-inner-spin-button,
input[type=number]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type=number] {
  -moz-appearance: textfield;
  appearance: textfield;
}

// Normal text style for MahaRERA entries (11, 11.a, 11.b, 11.c, 11.d)
:host ::ng-deep .ngx-datatable .datatable-body-row {
  .datatable-body-cell span {
    &[class*="maharera-cert"] {
      // Removed :has() selector as it's not widely supported
      font-style: normal !important;
      font-weight: normal !important;
      color: inherit !important;
    }
  }

  // Specific override for 11.a entry using attribute selectors instead of :has()
  .datatable-body-cell[data-maharera="11.a"] span,
  .datatable-body-cell[data-maharera="11.b"] span,
  .datatable-body-cell[data-maharera="11.c"] span,
  .datatable-body-cell[data-maharera="11.d"] span {
    font-style: normal !important;
    font-weight: normal !important;
    color: #212529 !important;
    padding-left: 0 !important;
  }
}

// Direct style for MahaRERA entries
.maharera-normal {
  font-style: normal !important;
  font-weight: normal !important;
  color: #212529 !important;
  padding-left: 0 !important;
  text-decoration: none !important;
  margin-left: 0 !important;
  text-indent: 0 !important;
}

// Override sub-item styling for MahaRERA entries
:host ::ng-deep .ngx-datatable .datatable-body-row {
  .text-center.sub-item[id*="11"] {
    font-style: normal !important;
    font-weight: normal !important;
    padding-left: 0 !important;
    margin-left: 0 !important;
    text-indent: 0 !important;
  }
}

// Alert styles
.alert-info {
  background-color: rgba(63, 130, 139, 0.1); // Light version of the preferred color
  border-color: rgba(63, 130, 139, 0.2);
  color: #3F828B;
}

// Follow-up section styles
.input-fields-section {
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: 5px;
  margin-bottom: 20px;
  border-left: 3px solid #3F828B;
  width: 100%;
  box-sizing: border-box;

  h6 {
    color: #3F828B;
    font-weight: 500;
    margin-bottom: 15px;
  }

  .form-control, .form-select {
    &:focus {
      border-color: #3F828B;
      box-shadow: 0 0 0 0.25rem rgba(63, 130, 139, 0.25);
    }
  }
}

.follow-up-history-section {
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: 5px;
  border-left: 3px solid #3F828B;
  width: 100%;
  box-sizing: border-box;

  h6 {
    color: #3F828B;
    font-weight: 500;
  }

  .table {
    th {
      background-color: #e9ecef;
      font-size: 0.85rem;
    }

    td {
      font-size: 0.85rem;
    }
  }
}

// Row detail styles
.row-detail-wrapper {
  width: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

.full-width-section {
  flex: 1;
  width: 100% !important;
  max-width: 100% !important;
}

// Override ngx-datatable styles
:host ::ng-deep .ngx-datatable {
  .datatable-row-detail {
    width: 100% !important;
    max-width: 100% !important;
    height: auto !important;
    overflow: visible !important;

    .datatable-row-detail-inner {
      width: 100% !important;
      max-width: 100% !important;
      height: auto !important;
      overflow: visible !important;
    }
  }

  // Center checkbox column content
  .datatable-body-cell.checkbox-column {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    text-align: center !important;
  }

  // Center checkbox column header
  .datatable-header-cell[title="Checkbox"] {
    text-align: center !important;
  }
}

// Tab styles and nested tab styles (agency-tabs) are now in global _custom.scss

// Dropdown styles
.dropdown-menu {
  max-height: 200px;
  overflow-y: auto;
}

/* NGX-Datatable styles */
:host ::ng-deep .ngx-datatable {
  box-shadow: none;
  border: 1px solid #dee2e6;
  border-radius: 0.375rem;
  overflow: hidden;
}

:host ::ng-deep .ngx-datatable.material {
  background-color: #fff;
  color: #212529;
}

:host ::ng-deep .ngx-datatable.material .datatable-header {
  background-color: #f8f9fa;
  border-bottom: 2px solid #dee2e6;
}

:host ::ng-deep .ngx-datatable.material .datatable-header .datatable-header-cell {
  font-weight: 500;
  padding: 0.75rem;
  text-align: left;
  color: #495057;
}

:host ::ng-deep .ngx-datatable.material .datatable-body .datatable-body-row {
  border-bottom: 1px solid #dee2e6;
}

:host ::ng-deep .ngx-datatable.material .datatable-body .datatable-body-cell {
  padding: 0.75rem;
}

:host ::ng-deep .ngx-datatable.material .datatable-body .datatable-row-detail {
  background-color: #f8f9fa;
  padding: 1rem;
  border-bottom: 1px solid #dee2e6;
}

:host ::ng-deep .ngx-datatable.material.expandable .datatable-row-detail-template {
  padding: 0.5rem 1rem;
}

/* Follow-up form and history styles */
.row-detail-container {
  background-color: #fff;
  border-radius: 0.25rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.follow-up-history-container, .follow-up-history-section {
  padding: 1rem;
  border-radius: 0.25rem;
  background-color: #fff;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);

  h6 {
    color: #495057;
    font-weight: 600;
  }

  .table {
    margin-bottom: 0;

    th {
      background-color: #f8f9fa;
      font-weight: 600;
      border-bottom: 2px solid #dee2e6;
    }

    td {
      vertical-align: middle;
    }
  }
}

.history-table th,
.history-table td {
  padding: 0.5rem;
  font-size: 0.875rem;
}

.follow-up-summary {
  font-size: 0.75rem;
  color: #6c757d;
}

/* Responsive adjustments */
@media (max-width: 992px) {
  :host ::ng-deep .ngx-datatable {
    width: 100%;
  }
}

@media (max-width: 768px) {
  :host ::ng-deep .ngx-datatable.material .datatable-header .datatable-header-cell,
  :host ::ng-deep .ngx-datatable.material .datatable-body .datatable-body-cell {
    padding: 0.5rem;
  }
}

@media (max-width: 576px) {
  .row-detail-container,
  .follow-up-history-container {
    padding: 0.5rem;
  }
}

// Responsive styles
@media (max-width: 767.98px) {
  .table-responsive-wrapper {
    overflow-x: auto;
  }

  // Tab responsive styles are now in global _custom.scss
}

// Institute Selection and In Principles styling

// In Principles form styling
.in-principles-content {
  .form-control, .form-select {
    border-color: #ced4da;
    &:focus {
      border-color: #3F828B;
      box-shadow: 0 0 0 0.25rem rgba(63, 130, 139, 0.25);
    }
  }

  textarea {
    resize: vertical;
  }

  // In Principles list styling
  .table {
    th {
      background-color: #f8f9fa;
      border-bottom-width: 1px;
      font-weight: 600;
      color: #495057;
    }

    td {
      vertical-align: middle;
    }

    .badge {
      font-weight: 500;
      padding: 0.35em 0.65em;
    }

    .btn-outline-primary, .btn-outline-secondary {
      padding: 0.25rem 0.5rem;
      line-height: 1;

      &:hover {
        color: white;
      }
    }

    .btn-outline-primary {
      &:hover {
        background-color: #df5316;
        border-color: #df5316;
      }
    }
  }

  // Email preview styling
  .email-preview {
    background-color: #f8f9fa;
    font-family: Arial, sans-serif;
    line-height: 1.6;
    color: #333;

    p {
      margin-bottom: 1rem;

      &:last-child {
        margin-bottom: 0;
      }
    }

    strong {
      font-weight: 600;
    }
  }
}

// Institute Selection dropdown styling
.institute-selection-content {
  .dropdown {
    .dropdown-menu {
      min-width: 280px;
      max-height: 300px;
      overflow-y: auto;
      padding: 8px;
      z-index: 1050; // Higher z-index to ensure it stays on top

      .px-2.py-1 {
        &:hover {
          background-color: rgba(0, 0, 0, 0.05);
          border-radius: 4px;
        }
      }

      .form-check {
        margin-bottom: 6px;
        display: block;
        justify-content: flex-start;
        padding-left: 0;
        width: 100%;

        &:last-child {
          margin-bottom: 0;
        }

        .form-check-input {
          cursor: pointer;
          margin-right: 8px;
          float: left;
          width: 18px;
          height: 18px;
          margin-top: 2px;

          &:checked {
            background-color: #df5316;
            border-color: #df5316;
          }

          &:focus {
            box-shadow: 0 0 0 0.25rem rgba(223, 83, 22, 0.25);
          }
        }

        .form-check-label {
          cursor: pointer;
          font-size: 0.9rem;
          padding-left: 4px;
          display: block;
          margin-left: 20px;
          user-select: none; // Prevent text selection
          white-space: normal; // Allow text to wrap
          word-break: break-word; // Break long words if needed
        }
      }
    }

    .btn-outline-secondary {
      width: 100%;
      text-align: left;
      position: relative;
      border-color: #ced4da;
      background-color: #fff;
      color: #212529;

      &:hover, &:focus {
        background-color: #f8f9fa;
        border-color: #ced4da;
        color: #212529;
      }

      &::after {
        position: absolute;
        right: 10px;
        top: 50%;
        transform: translateY(-50%);
      }
    }
  }

  // Selected Banks Table Styling
  .card {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    border: none;
    border-radius: 8px;
    overflow: hidden;
    margin-bottom: 1.5rem;

    .card-header {
      background-color: #f8f9fa;
      border-bottom: 1px solid #e9ecef;
      padding: 0.75rem 1rem;

      h6 {
        color: #3F828B;
        font-weight: 600;
        margin-bottom: 0;
      }
    }

    .table {
      margin-bottom: 0;

      th {
        background-color: #f8f9fa;
        font-weight: 600;
        color: #495057;
        border-top: none;
        padding: 0.75rem 1rem;
      }

      td {
        vertical-align: middle;
        padding: 0.75rem 1rem;
      }

      tr:hover {
        background-color: rgba(63, 130, 139, 0.03);
      }
    }
  }

  .btn-outline-danger {
    padding: 0.25rem 0.5rem;
    border-color: #dc3545;
    color: #dc3545;

    &:hover {
      background-color: #dc3545;
      color: white;
    }

    .icon-xs {
      stroke-width: 2.5px;
      width: 16px;
      height: 16px;
    }
  }

  // Responsive styles for institute selection
  @media (max-width: 767.98px) {
    .dropdown {
      .dropdown-menu {
        min-width: 100%;
        width: 100%;
      }
    }

    .card .table {
      th, td {
        padding: 0.5rem;
        font-size: 0.9rem;
      }
    }
  }

  @media (min-width: 768px) and (max-width: 991.98px) {
    .dropdown {
      .dropdown-menu {
        min-width: 250px;
      }
    }
  }
}

.notes-input {
  min-width: 250px;
  width: 100%;
}

.notes-column {
  min-width: 250px;
  width: 20%;
}

.status-select {
  min-width: 140px;
  width: 100%;
}

/* Ensure select dropdown options are fully visible */
select.status-select {
  width: 100%;
  min-width: 140px;
  text-overflow: ellipsis;
}

select.status-select option {
  width: auto;
  min-width: 140px;
}

.sr-no-column {
  width: 60px !important;
  min-width: 60px !important;
  max-width: 60px !important;
  text-align: center;
}

.checkbox-column {
  width: 60px !important;
  min-width: 60px !important;
  max-width: 60px !important;
  text-align: center;
}

.status-column {
  width: 140px !important;
  min-width: 140px !important;
  max-width: 140px !important;
}

.follow-up-column {
  width: 180px !important;
  min-width: 180px !important;
}

.action-column {
  width: 100px !important;
  min-width: 100px !important;
}

.list-name-column {
  width: 25%;
  min-width: 200px;
  white-space: normal;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

// Base style for list name cells
.table td:nth-child(2) {
  white-space: normal;
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
}

.conditional-column {
  width: 0;
  padding: 0;
  margin: 0;
  border: none;
  transition: all 0.3s ease;
}

/* When conditional columns are shown */
.conditional-column:not([style*="display: none"]) {
  width: auto;
  padding: 0.75rem;
  border: none;
}

.follow-up-data {
  font-size: 0.85rem;
  padding: 8px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border-left: 3px solid #3F828B;
}

// Custom scrollbar styling for CAM Note tabs
:host ::ng-deep {
  // Global scrollbar styling for the entire component
  ::-webkit-scrollbar {
    height: 4px; // Reduced height for horizontal scrollbar
    width: 6px; // Slightly wider for vertical scrollbar for better usability
  }

  ::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
  }

  ::-webkit-scrollbar-track {
    background-color: rgba(0, 0, 0, 0.05);
  }

  // Firefox scrollbar styling
  * {
    scrollbar-width: thin;
    scrollbar-color: rgba(0, 0, 0, 0.2) rgba(0, 0, 0, 0.05);
  }

  // Target CAM Note tabs 18 and 19 specifically with even thinner scrollbars
  li[ngbNavItem="18"] .tab-pane,
  li[ngbNavItem="19"] .tab-pane,
  [ngbNavOutlet="camNoteNav"] .tab-content {
    ::-webkit-scrollbar {
      height: 3px; // Extra thin horizontal scrollbar for these specific tabs
    }
  }
}

// Follow-up summary styling
.follow-up-summary {
  font-size: 0.8rem;
  padding: 4px 8px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border-left: 2px solid #3F828B;
  max-width: 250px;
}

// Follow-up form row styling
.follow-up-form-row, .follow-up-history-row {
  background-color: #f8f9fa;

  td {
    padding: 0 !important;
  }
}

// Follow-up container styling
.follow-up-container, .follow-up-history-container {
  padding: 15px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  margin-bottom: 15px;

  h6 {
    color: #495057;
    font-weight: 600;
  }

  .form-label {
    font-size: 0.85rem;
    font-weight: 500;
    color: #6c757d;
    margin-bottom: 0.25rem;
  }

  .form-control {
    &:focus {
      border-color: #3F828B;
      box-shadow: 0 0 0 0.25rem rgba(63, 130, 139, 0.25);
    }
  }

  .badge {
    font-weight: 500;
    background-color: #3F828B;
  }

  // History table styling
  .history-table {
    margin-bottom: 0;
    border-collapse: separate;
    border-spacing: 0;

    th {
      background-color: #f8f9fa;
      color: #495057;
      font-weight: 600;
      font-size: 0.85rem;
      padding: 0.5rem;
      border-bottom: 2px solid #e9ecef;
    }

    td {
      padding: 0.5rem;
      font-size: 0.85rem;
      border-bottom: 1px solid #f1f1f1;
      vertical-align: middle;
    }

    tr:last-child td {
      border-bottom: none;
    }

    tr:hover {
      background-color: rgba(223, 83, 22, 0.03);
    }
  }


}

// Modal styling for follow-up history
::ng-deep .modal-content {
  border: none;
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

// Full width section class
.full-width-section {
  width: 100% !important;
  max-width: 100% !important;
  display: block !important;
}

::ng-deep .modal-header {
  border-bottom: 1px solid #e9ecef;
  background-color: #f8f9fa;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}

::ng-deep .modal-title {
  color: #333;
  font-weight: 600;
}

::ng-deep .modal-body {
  padding: 1.5rem;
}

::ng-deep .modal-footer {
  border-top: 1px solid #e9ecef;
  background-color: #f8f9fa;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
}

// Ensure modal appears on top of everything
::ng-deep .modal {
  z-index: 1060 !important;
}

::ng-deep .modal-backdrop {
  z-index: 1050 !important;
}

// Specific styling for follow-up history modal
::ng-deep .follow-up-history-modal {
  .modal-dialog {
    max-width: 800px;
    margin: 1.75rem auto;
    animation: fadeInDown 0.3s ease-out;
  }

  .modal-content {
    border: none;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
  }

  .modal-header {
    color: #df5316;

    .modal-title {
      color: #df5316;
      font-weight: 600;
    }

    .btn-close {
      color: white;
      opacity: 0.8;
      &:hover {
        opacity: 1;
      }
    }
  }

  .table {
    margin-bottom: 0;

    th {
      background-color: #f8f9fa;
      font-weight: 600;
      border-bottom: 2px solid #dee2e6;
    }

    td {
      vertical-align: middle;
    }
  }



  // Empty state styling
  .text-center.py-3 {
    color: #6c757d;
    font-style: italic;
  }

  // Footer info text
  .text-muted.small {
    font-size: 0.85rem;
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translate3d(0, -30px, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

// Force column widths to be respected
.table th.sr-no-column, .table td.sr-no-column,
.table th.checkbox-column, .table td.checkbox-column {
  width: 60px !important;
  min-width: 60px !important;
  max-width: 60px !important;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-align: center;
}

// Center text in Sr No. column cells
.table td.sr-no-column {
  text-align: center;
}

.form-check {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0;
  padding: 0;
  text-align: center;
}

.form-check-input {
  margin: 0;
  float: none;
  position: relative;
  display: inline-block;
}

.table th.status-column, .table td.status-column {
  width: 140px !important;
  min-width: 140px !important;
  max-width: 140px !important;
  overflow: visible; /* Changed from hidden to visible for select elements */
  text-overflow: ellipsis;
  white-space: nowrap;
}

.date-input {
  min-width: 120px;
  width: 100%;
}

.time-input {
  min-width: 120px;
  width: 100%;
}

// Table cell styling
.table th, .table td {
  padding: 0.75rem;
  vertical-align: middle;
  white-space: normal;
  word-wrap: break-word;
  overflow-wrap: break-word;
  border-top: 1px solid #dee2e6;
  border-bottom: 1px solid #dee2e6;
  border-left: none;
  border-right: none;
}

// Style for table header cells
.table th {
  border-top: none;
  border-bottom: none;
  background-color: #f8f9fa;
  font-weight: 600;
}

// Style for specific header cells
.table th.sr-no-column,
.table th.list-name-column,
.table th.checkbox-column,
.table th.status-column,
.table th.conditional-column {
  border-bottom: none;
}

// Center text in specific header cells
.table th.sr-no-column,
.table th.checkbox-column {
  text-align: center;
}

// Style for action and follow-up header cells
.table th.action-column,
.table th.follow-up-column {
  border-bottom: none;
}

// Ensure long text in list name column wraps properly
.table td:nth-child(2), .list-name-cell {
  max-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: normal;
  word-break: break-word;
}

// Ensure table doesn't compress when conditional columns are shown/hidden
.table {
  table-layout: fixed;
  width: 100%;
}

.responsive-table {
  width: 100%;
  table-layout: fixed;
  border-collapse: collapse;
}

.icon-shape {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
}

.bg-light-primary {
  background-color: rgba(var(--bs-primary-rgb), 0.1);
}

.bg-light-success {
  background-color: rgba(var(--bs-success-rgb), 0.1);
}

.bg-light-info {
  background-color: rgba(var(--bs-info-rgb), 0.1);
}

.bg-light-warning {
  background-color: rgba(var(--bs-warning-rgb), 0.1);
}

// Timeline styles
.timeline {
  position: relative;
  padding-left: 30px;
}

.timeline-item {
  position: relative;
  padding-bottom: 25px;
}

.timeline-item:last-child {
  padding-bottom: 0;
}

.timeline-item:before {
  content: '';
  position: absolute;
  left: -20px;
  top: 0;
  bottom: 0;
  width: 2px;
  background-color: #e9ecef;
}

.timeline-item-icon {
  position: absolute;
  left: -30px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
}

.timeline-item-content {
  padding-bottom: 10px;
}

.timeline-item:last-child .timeline-item-content {
  padding-bottom: 0;
}

// Responsive table wrapper
.table-responsive-wrapper {
  position: relative;
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  margin-bottom: 1rem;
  border-radius: 0.25rem;
  max-width: 100%;
}

// Large devices (desktops, less than 1200px)
@media (max-width: 1199.98px) {
  .table th, .table td {
    min-width: 100px; // Ensure minimum width for all cells on large screens
  }

  .table th.sr-no-column, .table td.sr-no-column {
    position: sticky;
    left: 0;
    background-color: #fff;
    z-index: 2;
  }

  .list-name-column {
    min-width: 180px;
  }

  .notes-input {
    min-width: 200px;
  }
}

// Medium devices (tablets, less than 992px)
@media (max-width: 991.98px) {
  .table th, .table td {
    padding: 0.6rem;
    font-size: 0.9rem;
  }

  .sr-no-column {
    width: 50px !important;
    min-width: 50px !important;
    max-width: 50px !important;
  }

  .checkbox-column {
    width: 50px !important;
    min-width: 50px !important;
    max-width: 50px !important;
  }

  .status-column {
    width: 120px !important;
    min-width: 120px !important;
    max-width: 120px !important;
  }

  .follow-up-column {
    width: 160px !important;
    min-width: 160px !important;
  }

  .list-name-column {
    min-width: 150px;
  }
}

// Small devices (landscape phones, 576px-767px)
@media (min-width: 576px) and (max-width: 767.98px) {
  .table th, .table td {
    padding: 0.5rem;
    font-size: 0.85rem;
  }

  .btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
  }

  .form-control-sm, .form-select-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
  }

  .follow-up-data {
    font-size: 0.75rem;
    padding: 5px;
  }

  .list-name-column {
    min-width: 130px;
    width: 30% !important;
    max-width: 180px;
    white-space: normal;
    word-wrap: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
  }

  // Make list name text wrap properly
  .table td:nth-child(2) {
    white-space: normal;
    word-wrap: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
    font-size: 0.8rem;
    line-height: 1.2;
  }
}

// Medium-small devices (481px-575px)
@media (min-width: 481px) and (max-width: 575.98px) {
  .list-name-column {
    min-width: 110px;
    width: 28% !important;
    max-width: 140px;
  }

  .table td:nth-child(2) {
    font-size: 0.78rem;
    line-height: 1.15;
    padding: 0.35rem;
  }

  .sr-no-column {
    width: 40px !important;
    min-width: 40px !important;
    max-width: 40px !important;
  }

  .checkbox-column {
    width: 40px !important;
    min-width: 40px !important;
    max-width: 40px !important;
  }

  .status-column {
    width: 100px !important;
    min-width: 100px !important;
    max-width: 100px !important;
  }

  .follow-up-column {
    width: 140px !important;
    min-width: 140px !important;
  }
}

// Extra small devices (portrait phones, less than 481px)
@media (max-width: 480px) {
  .table th, .table td {
    padding: 0.4rem;
    font-size: 0.8rem;
  }

  .sr-no-column {
    width: 40px !important;
    min-width: 40px !important;
    max-width: 40px !important;
  }

  .checkbox-column {
    width: 40px !important;
    min-width: 40px !important;
    max-width: 40px !important;
  }

  .status-column {
    width: 100px !important;
    min-width: 100px !important;
    max-width: 100px !important;
  }

  .follow-up-column {
    width: 140px !important;
    min-width: 140px !important;
  }

  .btn-sm {
    padding: 0.2rem 0.4rem;
    font-size: 0.7rem;
  }

  .form-control-sm, .form-select-sm {
    padding: 0.2rem 0.4rem;
    font-size: 0.7rem;
  }

  .follow-up-data {
    font-size: 0.7rem;
    padding: 4px;
  }

  // Optimize table wrapper for very small screens
  .table-responsive-wrapper {
    margin: 0;
    padding: 0;
    border-radius: 0;
  }

  .list-name-column {
    min-width: 100px;
    width: 25% !important;
    max-width: 150px;
    white-space: normal;
    word-wrap: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
  }

  // Make list name text wrap properly
  .table td:nth-child(2) {
    white-space: normal;
    word-wrap: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
    font-size: 0.75rem;
    line-height: 1.1;
    padding: 0.3rem;
  }
}

// Small mobile devices (376px-480px)
@media (min-width: 376px) and (max-width: 480px) {
  .list-name-column {
    min-width: 95px;
    width: 24% !important;
    max-width: 130px;
  }

  .table td:nth-child(2) {
    font-size: 0.72rem;
    line-height: 1.05;
    padding: 0.28rem;
  }

  .sr-no-column {
    width: 35px !important;
    min-width: 35px !important;
    max-width: 35px !important;
  }

  .checkbox-column {
    width: 35px !important;
    min-width: 35px !important;
    max-width: 35px !important;
  }

  .status-column {
    width: 90px !important;
    min-width: 90px !important;
    max-width: 90px !important;
  }

  .follow-up-column {
    width: 130px !important;
    min-width: 130px !important;
  }
}

// Very small devices (small phones, less than 376px)
@media (max-width: 375px) {
  .list-name-column {
    min-width: 90px;
    width: 22% !important;
    max-width: 120px;
  }

  .table td:nth-child(2) {
    font-size: 0.7rem;
    line-height: 1;
    padding: 0.25rem;
  }

  .sr-no-column {
    width: 30px !important;
    min-width: 30px !important;
    max-width: 30px !important;
  }

  .checkbox-column {
    width: 30px !important;
    min-width: 30px !important;
    max-width: 30px !important;
  }

  .status-column {
    width: 80px !important;
    min-width: 80px !important;
    max-width: 80px !important;
  }

  .follow-up-column {
    width: 120px !important;
    min-width: 120px !important;
  }
}

/* Component-specific styles */

/* Debt Details Table Styles */
.debt-details-table, .other-business-table {
  width: 100%;

  .datatable-header {
    .datatable-header-cell {
      text-align: left;
      font-weight: 600;
      color: #495057;
      background-color: #f8f9fa;
      padding: 0.75rem;
      border-bottom: 2px solid #dee2e6;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .datatable-body {
    .datatable-body-row {
      &:nth-child(even) {
        background-color: rgba(0, 0, 0, 0.02);
      }

      &:hover {
        background-color: rgba(223, 83, 22, 0.05);
      }
    }

    .datatable-body-cell {
      padding: 0.75rem;
      border-bottom: 1px solid #dee2e6;

      .text-nowrap {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .text-wrap {
        white-space: normal;
        word-break: break-word;
      }

      .text-end {
        text-align: right;
      }

      .text-center {
        text-align: center;
      }
    }
  }
}

/* Other Business Table and Group Entities Table Specific Styles */
.other-business-table, .group-entities-table {
  // Ensure proper text wrapping for longer fields
  .text-wrap {
    white-space: normal;
    word-break: break-word;
    max-width: 100%;
    overflow-wrap: break-word;
  }

  // Ensure proper alignment for numeric values
  .text-end {
    text-align: right;
  }

  // Ensure proper alignment for centered values
  .text-center {
    text-align: center;
  }

  // Ensure proper styling for the table header
  .datatable-header-cell {
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.85rem;
  }

  // Ensure proper styling for the table cells
  .datatable-body-cell {
    font-size: 0.9rem;

    // Style for PAN and CIN/GST numbers
    span:has(+ .pan, + .cin-gst) {
      font-family: monospace;
      letter-spacing: 0.5px;
    }
  }

  // Add a subtle hover effect for rows
  .datatable-row-wrapper:hover {
    background-color: rgba(63, 130, 139, 0.05);
  }
}

/* Group Entities Table Specific Styles */
.group-entities-table {
  // Style for the edit button
  .btn-outline-primary {
    color: #df5316;
    border-color: #df5316;

    &:hover {
      background-color: #df5316;
      color: white;
    }
  }

  // Style for relationship badges
  .relationship-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 500;

    &.parent {
      background-color: rgba(13, 110, 253, 0.1);
      color: #0d6efd;
    }

    &.subsidiary {
      background-color: rgba(25, 135, 84, 0.1);
      color: #198754;
    }

    &.sister {
      background-color: rgba(108, 117, 125, 0.1);
      color: #6c757d;
    }

    &.joint-venture {
      background-color: rgba(255, 193, 7, 0.1);
      color: #ffc107;
    }
  }
}

/* Completed Projects Table Specific Styles */
.completed-projects-table {
  .datatable-body-row {
    cursor: pointer;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: rgba(223, 83, 22, 0.05);
    }

    &.active {
      background-color: rgba(223, 83, 22, 0.1);
    }
  }
}

/* Unsold Stock & Leased Prop Table Specific Styles */
.unsold-stock-table {
  .datatable-body-row {
    cursor: pointer;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: rgba(223, 83, 22, 0.05);
    }

    &.active {
      background-color: rgba(223, 83, 22, 0.1);
    }
  }

  .text-success {
    color: #28a745 !important;
  }

  .text-warning {
    color: #ffc107 !important;
  }
}

/* Leased Properties Table Specific Styles */
.leased-properties-table {
  .datatable-body-row {
    cursor: pointer;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: rgba(223, 83, 22, 0.05);
    }

    &.active {
      background-color: rgba(223, 83, 22, 0.1);
    }
  }
}

/* Compiled Cost Table Specific Styles */
.compiled-cost-table {
  .datatable-body-row {
    cursor: pointer;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: rgba(223, 83, 22, 0.05);
    }

    &.active {
      background-color: rgba(223, 83, 22, 0.1);
    }
  }

  .datatable-footer {
    background-color: #f8f9fa;
    border-top: 1px solid #dee2e6;

    .text-end {
      font-weight: bold;
    }
  }
}

/* Fund Infusion Table Specific Styles */
.fund-infusion-table {
  .datatable-body-row {
    cursor: pointer;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: rgba(223, 83, 22, 0.05);
    }

    &.active {
      background-color: rgba(223, 83, 22, 0.1);
    }
  }

  .datatable-footer {
    background-color: #f8f9fa;
    border-top: 1px solid #dee2e6;

    .text-end {
      font-weight: bold;
    }
  }
}

/* Land Bank Table Specific Styles */
.land-bank-table {
  .datatable-body-row {
    cursor: pointer;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: rgba(223, 83, 22, 0.05);
    }

    &.active {
      background-color: rgba(223, 83, 22, 0.1);
    }
  }

  .datatable-footer {
    background-color: #f8f9fa;
    border-top: 1px solid #dee2e6;

    strong {
      font-weight: bold;
    }
  }

  .badge {
    font-size: 0.75rem;
    padding: 0.35em 0.65em;
  }
}

/* Land Bank Detail Table Specific Styles */
.land-bank-detail-table {
  .datatable-body-row {
    cursor: pointer;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: rgba(223, 83, 22, 0.05);
    }

    &.active {
      background-color: rgba(223, 83, 22, 0.1);
    }
  }

  .datatable-footer {
    background-color: #f8f9fa;
    border-top: 1px solid #dee2e6;

    strong {
      font-weight: bold;
    }
  }

  .badge {
    font-size: 0.75rem;
    padding: 0.35em 0.65em;
  }
}

/* Project Land Detail Table Specific Styles */
.project-land-detail-table {
  .datatable-body-row {
    cursor: pointer;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: rgba(223, 83, 22, 0.05);
    }

    &.active {
      background-color: rgba(223, 83, 22, 0.1);
    }
  }

  .datatable-footer {
    background-color: #f8f9fa;
    border-top: 1px solid #dee2e6;

    strong {
      font-weight: bold;
    }
  }

  .badge {
    font-size: 0.75rem;
    padding: 0.35em 0.65em;
  }
}

/* Expandable table styles for multiple follow-ups */
:host ::ng-deep .ngx-datatable.expandable-table {
  .datatable-row-detail {
    background-color: #f8f9fa;
    border-top: 1px solid #dee2e6;
    border-bottom: 1px solid #dee2e6;
    overflow: hidden;
  }

  .row-detail-wrapper {
    padding: 1rem;
    background-color: #f8f9fa;
  }

  .expand-icon {
    font-size: 1rem;
    color: #df5316;
  }

  .btn-link {
    color: #df5316;
    &:hover {
      color: darken(#df5316, 10%);
    }
    &.expanded {
      transform: rotate(90deg);
    }
  }

  .input-fields-section {
    background-color: #fff;
    border-radius: 0.375rem;
    padding: 1rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    margin-bottom: 1rem;

    h6 {
      color: #3F828B;
      font-weight: 600;
    }

    .form-label {
      font-size: 0.85rem;
      font-weight: 500;
      color: #6c757d;
      margin-bottom: 0.25rem;
    }

    .form-control {
      &:focus {
        border-color: #df5316;
        box-shadow: 0 0 0 0.25rem rgba(223, 83, 22, 0.25);
      }
    }
  }

  .follow-up-history-section {
    background-color: #fff;
    border-radius: 0.375rem;
    padding: 1rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border-left: 3px solid #3F828B;

    h6 {
      color: #3F828B;
      font-weight: 600;
      margin-bottom: 1rem;
    }

    .table {
      margin-bottom: 0;

      th {
        background-color: #f8f9fa;
        font-weight: 600;
        font-size: 0.85rem;
        border-bottom: 2px solid #dee2e6;
      }

      td {
        font-size: 0.85rem;
        vertical-align: middle;
      }

      tr:hover {
        background-color: rgba(223, 83, 22, 0.05);
      }
    }
  }
}