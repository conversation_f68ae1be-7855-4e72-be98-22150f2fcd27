import { Component, Directive, EventEmitter, Input, OnInit, Output, QueryList, ViewChild, ViewChildren } from '@angular/core';
import { CommonModule, DecimalPipe } from '@angular/common';
import { RouterLink, Router, NavigationEnd, ActivatedRoute } from '@angular/router';
import { FormControl, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgbPaginationModule, NgbTooltipModule, NgbDropdownModule, NgbNavModule, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { FeatherIconDirective } from '../../../../core/feather-icon/feather-icon.directive';
import { ColumnMode, DatatableComponent, NgxDatatableModule, SelectionType } from '@siemens/ngx-datatable';
import { SathbaraEntryModalComponent } from './sathbara-entry-modal/sathbara-entry-modal.component';
import { GroupEntityModalComponent } from './group-entity-modal/group-entity-modal.component';
import { CompletedProjectModalComponent } from './completed-project-modal/completed-project-modal.component';
import { ProjectLandModalComponent } from './project-land-modal/project-land-modal.component';
import { FollowUpHistoryModalComponent } from './follow-up-history-modal/follow-up-history-modal.component';
import { OtherBusinessModalComponent } from './other-business-modal/other-business-modal.component';
import { SalesPlanModalComponent } from './sales-plan-modal/sales-plan-modal.component';
import { DebtDetailsModalComponent } from './debt-details-modal/debt-details-modal.component';
import { GenericModalComponent } from './generic-modal/generic-modal.component';
import { LandBankModalComponent } from './land-bank-modal/land-bank-modal.component';
import { LandBankDetailModalComponent } from './land-bank-detail-modal/land-bank-detail-modal.component';
import { PeoplesData } from '../../../../core/dummy-datas/peoples.data';
import { ContentChange, QuillModule, SelectionChange } from 'ngx-quill';
import { NgLabelTemplateDirective, NgOptionTemplateDirective, NgSelectComponent } from '@ng-select/ng-select';

// Sortable directive
export type SortColumn = keyof OpsTeamData | '';
export type SortDirection = 'asc' | 'desc' | '';

const rotate: { [key: string]: SortDirection } = { 'asc': 'desc', 'desc': '', '': 'asc' };

export interface SortEvent {
  column: SortColumn;
  direction: SortDirection;
}

// Shareholder interface for Initial KYC
interface Shareholder {
  id: number;
  name: string;
  shareholdingPercentage: string;
  profitSharingRatio: string;
  asPerPdDate: string;
  pan: string;
  aadharNo: string;
  dateOfBirth: string;
  functionsHandled: string;
  yearsInRealEstate: string;
  yearsInGroup: string;
  fatherName: string;
  motherName: string;
  motherMaidenName: string;
  spouseName: string;
  childrenNames: string;
  contactNumber: string;
  emailId: string;
  residentialAddress: string;
  latestNetWorth: string;
  detailedProfile: string;
}

@Directive({
  selector: 'th[sortable]',
  standalone: true,
  host: {
    '[class.asc]': 'direction === "asc"',
    '[class.desc]': 'direction === "desc"',
    '(click)': 'rotate()'
  }
})
export class NgbdSortableHeader {
  @Input() sortable: SortColumn = '';
  @Input() direction: SortDirection = '';
  @Output() sort = new EventEmitter<SortEvent>();

  rotate() {
    this.direction = rotate[this.direction];
    this.sort.emit({ column: this.sortable, direction: this.direction });
  }
}

// Ops Team data interface
export interface OpsTeamData {
  id: number;
  uniqueId: string;
  leadName: string;
  companyName: string;
  projectName: string;
  productType: string;
  documentStatus: string;
  onePagerStatus: string;
  camNoteStatus: string;
  instituteSelectionStatus: string;
  inPrinciplesStatus: string;
  cashflowStatus: string;
  createdDate: string;
  lastUpdated: string;
}

// Sample data
const OPS_TEAM_DATA: OpsTeamData[] = [
  {
    id: 1,
    uniqueId: 'BC-2025-001',
    leadName: 'L&T',
    companyName: 'L&T',
    projectName: 'L&T Parel',
    productType: 'CF/PF/IF',
    documentStatus: 'Completed',
    onePagerStatus: 'In Progress',
    camNoteStatus: 'Pending',
    instituteSelectionStatus: 'Pending',
    inPrinciplesStatus: 'Pending',
    cashflowStatus: 'Pending',
    createdDate: '15 Jun 2023',
    lastUpdated: '22 Aug 2023'
  },
  {
    id: 2,
    uniqueId: 'BC-2025-002',
    leadName: 'Hiranandani',
    companyName: 'Hiranandani Developers',
    projectName: 'Lake Enclave',
    productType: 'HL/LAP/LRD/NRPL',
    documentStatus: 'Completed',
    onePagerStatus: 'Completed',
    camNoteStatus: 'Completed',
    instituteSelectionStatus: 'In Progress',
    inPrinciplesStatus: 'Pending',
    cashflowStatus: 'Pending',
    createdDate: '10 May 2023',
    lastUpdated: '15 Jul 2023'
  },
  {
    id: 3,
    uniqueId: 'BC-2025-003',
    leadName: 'Lodha Group',
    companyName: 'Lodha Developers',
    projectName: 'World Towers',
    productType: 'Insurance',
    documentStatus: 'In Progress',
    onePagerStatus: 'Pending',
    camNoteStatus: 'Pending',
    instituteSelectionStatus: 'Pending',
    inPrinciplesStatus: 'Pending',
    cashflowStatus: 'Pending',
    createdDate: '05 Apr 2023',
    lastUpdated: '20 Jun 2023'
  },
  {
    id: 4,
    uniqueId: 'BC-2025-004',
    leadName: 'Oberoi Realty',
    companyName: 'Oberoi Group',
    projectName: 'Sky City',
    productType: 'Property',
    documentStatus: 'Completed',
    onePagerStatus: 'Completed',
    camNoteStatus: 'Completed',
    instituteSelectionStatus: 'Completed',
    inPrinciplesStatus: 'Completed',
    cashflowStatus: 'Completed',
    createdDate: '12 Mar 2023',
    lastUpdated: '18 May 2023'
  },
  {
    id: 5,
    uniqueId: 'BC-2025-005',
    leadName: 'Godrej Properties',
    companyName: 'Godrej Group',
    projectName: 'Godrej Central',
    productType: 'CF/PF/IF',
    documentStatus: 'Completed',
    onePagerStatus: 'Completed',
    camNoteStatus: 'In Progress',
    instituteSelectionStatus: 'Pending',
    inPrinciplesStatus: 'Pending',
    cashflowStatus: 'Pending',
    createdDate: '20 Feb 2023',
    lastUpdated: '10 Apr 2023'
  }
];

// Helper function for filtering
function search(text: string, pipe: DecimalPipe): OpsTeamData[] {
  return OPS_TEAM_DATA.filter(item => {
    const term = text.toLowerCase();
    return item.uniqueId.toLowerCase().includes(term)
        || item.leadName.toLowerCase().includes(term)
        || item.companyName.toLowerCase().includes(term)
        || item.projectName.toLowerCase().includes(term)
        || item.productType.toLowerCase().includes(term)
        || item.documentStatus.toLowerCase().includes(term)
        || item.onePagerStatus.toLowerCase().includes(term)
        || item.camNoteStatus.toLowerCase().includes(term)
        || item.instituteSelectionStatus.toLowerCase().includes(term)
        || item.inPrinciplesStatus.toLowerCase().includes(term)
        || item.cashflowStatus.toLowerCase().includes(term);
  });
}

// Helper function for sorting
function compare(v1: string | number, v2: string | number) {
  return (v1 < v2 ? -1 : v1 > v2 ? 1 : 0);
}

// Define the type for Sales Plan rows
interface SalesPlanRow {
  id: number;
  particulars: string;
  q1: number;
  q2: number;
  q3: number;
  q4: number;
  q5: number;
  q6: number;
  q7: number;
  q8: number;
  q9: number;
  [key: string]: any; // Index signature to allow string indexing
}

// Define the type for Debt Details rows
interface DebtDetailsRow {
  id: number;
  borrowerName: string;
  entityType: string;
  nameOfLender: string;
  typeOfFacility: string;
  loanAccountNo: string;
  individualGuarantee: string;
  dateOfSanction: string;
  dateOfDisbursement: string;
  sanctionedAmount: number;
  disbursedAmount: number;
  utilized: number;
  roa: number;
  repaid: number;
  emiAmount: number;
  repaymentBankAccountNo: string;
  totalEmi: number;
  emiPaid: number;
  originFees: number;
  currentOutstanding: number;
  loanTenor: string;
  moratorium: string;
  detailsOfSecurityCreated: string;
  overdueAmount: number;
  detailsOfDefault: string;
  remarksForDelay: string;
  [key: string]: any; // Index signature to allow string indexing
}

// Define the type for Other Business Details rows
interface OtherBusinessRow {
  id: number;
  promoterName: string;
  companyName: string;
  constitution: string;
  dateOfIncorporation: string;
  pan: string;
  cinGstNo: string;
  location: string;
  lineOfActivity: string;
  yearsOfExperience: number;
  avgAnnualTurnover: number;
  avgAnnualProfit: number;
  [key: string]: any; // Index signature to allow string indexing
}

// Define the type for Group Entities rows
interface GroupEntityRow {
  id: number;
  companyName: string;
  constitution: string;
  dateOfIncorporation: string;
  pan: string;
  cinGstNo: string;
  registeredOfficeAddress: string;
  partnersDirectors: string;
  profitSharingRatio: string;
  din: string;
  currentProjects: string;
  [key: string]: any; // Index signature to allow string indexing
}

// Define the type for Sathbara & Mutation Entries rows
interface SathbaraEntryRow {
  id: number;
  surveyNumber: string;
  area: string;
  mutationEntries: string;
  landOwnerName: string;
  remarks: string;
  [key: string]: any; // Index signature to allow string indexing
}

// Define the type for Completed Projects rows
export interface CompletedProjectRow {
  id: number;
  projectName: string;
  entityName: string;
  promotersNames: string;
  profitSharing: string;
  promotersRole: string;
  developmentType: string;
  location: string;
  projectType: string;
  projectStructure: string;
  surveyNumbers: string;
  totalUnits: number;
  constructionArea: string;
  constructionCost: number;
  salesValue: number;
  unsoldUnits: number;
  startDate: string;
  endDate: string;
  occupancyReceived: string;
  reraNumber: string;
  remarks: string;
  [key: string]: any; // Index signature to allow string indexing
}

// Define the type for Ongoing Projects rows
export interface OngoingProjectRow {
  id: number;
  projectName: string;
  entityName: string;
  promotersNames: string;
  projectStructure: string; // ENTIRE PROJECT STRUCTURE
  constructionApprovals: string; // CURRENT CONSTRUCTION APPROVALS
  latestCCDate: string; // DATE OF LATEST CC
  projectCategory: string; // NORMAL LAYOUT / SOC REDEV / SRA REDEV / MHADA REDEV / CESSED REDEV
  ownershipType: string; // OWNED / JDA / JV
  plotArea: string; // LAND / PLOT AREA
  category: string; // CATEGORY
  sharingDetails: string; // SHARING DETAILS (%)
  area1: number; // AREA (SQ FT) 1
  units1: number; // UNITS 1
  area2: number; // AREA (SQ FT) 2
  units2: number; // UNITS 2
  area3: number; // AREA (SQ FT) 3
  units3: number; // UNITS 3
  soldArea: number; // SOLD AREA (SQ FT)
  unsoldArea: number; // UNSOLD AREA (SQ FT)
  soldUnits: number; // SOLD UNITS
  unsoldUnits: number; // UNSOLD UNITS
  totalEstimatedRevenue: number; // TOTAL ESTIMATED REVENUE
  valueOfAreaUnsold: number; // VALUE OF AREA UNSOLD
  valueOfAreaSold: number; // VALUE OF AREA SOLD
  receiptsFromSoldArea: number; // RECEIPTS FROM SOLD AREA
  receivablesFromSoldArea: number; // RECEIVABLES FROM SOLD AREA
  totalCost: number; // TOTAL
  costIncurred: number; // COST INCURRED
  costToBeIncurred: number; // COST TO BE INCURRED
  promoterSkinInfusion: number; // PROMOTER'S SKIN (INFUSION)
  profitInCrores: number; // PROFIT (RS IN CRS)
  profitPercentOnRevenue: number; // PROFIT % ON REVENUE
  surplusInCrores: number; // SURPLUS (RS IN CRS)
  rateSold: number; // RATE (SOLD)
  rateUnsold: number; // RATE (UNSOLD)
  percentCompletion: number; // % COMPLETION
  percentCollections: number; // % COLLECTIONS
  existingLoan: string; // EXISTING LOAN (IF ANY)
  bankName: string; // NAME OF BANK/FI
  sanctionDate: string; // SANCTION DATE
  rateOfInterest: number; // ROI (%)
  amountSanctioned: number; // AMOUNT SANCTIONED
  amountDisbursed: number; // AMOUNT DISBURSED
  currentOutstandingAmount: number; // CURRENT OUTSTANDING AMOUNT
  moratPeriod: string; // MORAT PERIOD
  repaymentStartingDate: string; // REPAYMENT STARTING DATE
  projectStartDate: string; // START
  projectLaunchDate: string; // LAUNCH
  currentPhysicalStage: string; // CURRENT PHYSICAL STAGE OF PROJECT (AS ON __/__/____)
  projectCompletionDate: string; // COMPLETION
  profitSharing: string;
  developmentType: string;
  location: string;
  projectType: string;
  surveyNumbers: string; // Added for S NO & H NO / CTS NO
  totalUnits: number;
  constructionArea: string;
  constructionCost: number;
  salesValue: number;
  startDate: string;
  expectedEndDate: string;
  reraNumber: string;
  approvalStatus: string;
  constructionStatus: string;
  promoterSkin: number;
  profit: number;
  profitPercentage: number;
  surplus: number;
  rates: string;
  completionPercentage: number;
  collectionPercentage: number;
  remarks: string;
  [key: string]: any; // Index signature to allow string indexing
}

// Define the type for Unsold Stock & Leased Prop rows
export interface UnsoldStockRow {
  id: number;
  projectName: string;
  location: string;
  category: string; // Residential/Commercial/Retail/Industrial
  saleableArea: number;
  rate: number;
  value: number;
  debt: number;
  remarks: string;
  [key: string]: any; // Index signature to allow string indexing
}

// Define the type for Leased Properties rows
export interface LeasedPropertyRow {
  id: number;
  projectName: string;
  location: string;
  areaLeased: number;
  leaseRentPA: number;
  securityDeposit: number;
  debtOS: number;
  marketValue: number;
  remarks: string;
  [key: string]: any; // Index signature to allow string indexing
}

// Define the type for Compiled Cost rows
export interface CompiledCostRow {
  id: number;
  particulars: string;
  totalCost: number;
  costIncurred: number;
  costToBeIncurred: number;
  percentage: number;
  [key: string]: any; // Index signature to allow string indexing
}

// Define the type for Fund Infusion rows
export interface FundInfusionRow {
  id: number;
  particulars: string;
  total: number;
  infusion: number;
  infusionTillDate: number;
  toBeInfused: number;
  percentage: number;
  [key: string]: any; // Index signature to allow string indexing
}

// Define the type for Land Bank & Upcoming Projects rows
export interface LandBankRow {
  id: number;
  projectName: string;
  entityName: string;
  ownershipType: string;
  documentationStatus: string;
  location: string;
  projectType: string;
  expectedStartDate: string;
  residentialArea: number;
  residentialUnits: number;
  commercialArea: number;
  commercialUnits: number;
  mixedArea: number;
  mixedUnits: number;
  remarks: string;
  [key: string]: any; // Index signature to allow string indexing
}

// Define the type for Land Bank Details rows
export interface LandBankDetailRow {
  id: number;
  companyName: string;
  location: string;
  landType: string;
  ownershipType: string;
  plotArea: number;
  plotAreaUnit: string;
  acquisitionYear: string;
  purchaseValue: number;
  marketValue: number;
  hasLoan: string;
  approvalStatus: string;
  expectedLaunchDate: string;
  remarks: string;
  [key: string]: any; // Index signature to allow string indexing
}

// Define the type for Sales MIS & Inventory rows
export interface SalesMisRow {
  id: number;
  buildingWingName: string;
  floorNo: string;
  unitNo: string;
  mahareraCarpetArea: number;
  carpetArea: number;
  saleableBuiltUpArea: number;
  unitType: string; // TYPE OF UNIT (COMM / RESI)
  ownerType: string; // DEVELOPER / LANDOWNER / TENANT
  undividedShareOfLand: number; // UNDIVIDED SHARE OF LAND
  extraCarpetAreaSoldToExistingTenants: number; // EXTRA CARPET AREA SOLD TO EXISTING TENANTS
  extraSaleableAreaSoldToExistingTenants: number; // EXTRA SALEABLE AREA SOLD TO EXISTING TENANTS
  saleStatus: string; // SOLD / UNSOLD / MORTGAGED
  buyerName: string; // NAME OF THE BUYER
  contactNo: string; // CONTACT NO
  emailId: string; // EMAIL ID
  dateOfBooking: string; // DATE OF BOOKING
  dateOfRegistration: string; // DATE OF REGISTRATION
  registrationNo: string; // REGISTRATION / AGREEMENT NO
  basicCost: number; // BASIC COST
  developmentCostComponents: number; // DEVELOPMENT COST COMPONENTS
  totalValueOfUnit: number; // TOTAL VALUE OF UNIT
  demandRaised: number; // DEMAND RAISED
  amountReceived: number; // AMOUNT RECEIVED
  balance: number; // BALANCE
  stageOfConstruction: string; // STAGE OF CONSTRUCTION
  collections: number; // COLLECTIONS
  homeLoanFinancierName: string; // HOME LOAN FINANCIER'S NAME
  nocIssuedByEarlierFinancier: string; // NOC ISSUED BY EARLIER FINANCIER IN CASE OF BT TAKE OVER
  typeOfCustomer: string; // TYPE OF CUSTOMER
  [key: string]: any; // Index signature to allow string indexing
}

// Define the type for Sales MIS & Inventory Summary rows
export interface SalesMisSummaryRow {
  id: number;
  saleableBuiltUpArea: number; // SALEABLE BUILT-UP AREA
  mahareraCarpetAreaSqMt: number; // MAHARERA CARPET AREA (Sq.Mt.)
  carpetAreaSqFt: number; // CARPET AREA (Sq.Ft.)
  numberOfFlats: number; // NUMBER OF FLATS
  numberOfShops: number; // NUMBER OF SHOPS
  numberOfOffices: number; // NUMBER OF OFFICES
  refugeUnits: number; // REFUGE UNITS
  achievedForSoldArea: number; // ACHIEVED FOR SOLD AREA (AS PER CARPET AREA)
  targetForUnsoldArea: number; // TARGET FOR UNSOLD AREA (AS PER CARPET AREA)
  [key: string]: any; // Index signature to allow string indexing
}

// Define the type for Project Land Details rows
export interface ProjectLandDetailRow {
  id: number;
  agreementType: string;
  documentDate: string;
  documentNo: string;
  surveyNo: string;
  areaSqMtrs: number;
  areaGunthaAcre: number;
  areaUnit: string;
  partiesNames: string;
  considerationDetails: string;
  [key: string]: any; // Index signature to allow string indexing
}

@Component({
  selector: 'app-ops-team',
  standalone: true,
  imports: [
    CommonModule,
    RouterLink,
    FormsModule,
    ReactiveFormsModule,
    NgbPaginationModule,
    NgbTooltipModule,
    NgbDropdownModule,
    NgbNavModule,
    FeatherIconDirective,
    NgxDatatableModule,
    NgbdSortableHeader,
    FollowUpHistoryModalComponent,
    OtherBusinessModalComponent,
    SalesPlanModalComponent,
    DebtDetailsModalComponent,
    SathbaraEntryModalComponent,
    GroupEntityModalComponent,
    CompletedProjectModalComponent,
    ProjectLandModalComponent,
    GenericModalComponent,
    LandBankModalComponent,
    LandBankDetailModalComponent,
    NgLabelTemplateDirective,
    NgOptionTemplateDirective,
    NgSelectComponent,
    QuillModule
  ],
  templateUrl: './ops-team.component.html',
  styleUrls: ['./ops-team.component.scss'],
  providers: [DecimalPipe]
})
export class OpsTeamComponent implements OnInit {
  // View mode flag
  showListView = true;
  showDetailsForm = false;
  selectedItemId: number | null = null;

  // Original data
  opsTeamData = OPS_TEAM_DATA;

  // Filtered data
  filteredOpsTeam: OpsTeamData[] = [];

  // Search filter
  searchTerm = new FormControl('', { nonNullable: true });

  // Pagination
  page = 1;
  pageSize = 5;
  collectionSize = OPS_TEAM_DATA.length;

  // Make Math available in template
  Math = Math;

  // Sorting
  @ViewChildren(NgbdSortableHeader) headers: QueryList<NgbdSortableHeader>;

  // Active tab IDs
  activeTabId = 1; // Set to Document Status tab
  activeCamNoteTabId = 1; // For nested tabs in CAM Note

  // Flag to track if any document has "received" status
  showReceivedDateColumn = false;

  // Accordion states for Summary tab
  proposalExpanded = true;
  initialKycExpanded = false;
  borrowersDetailsExpanded = false;
  otherBusinessDetailsExpanded = false;
  projectLandDetailsExpanded = false;
  sathbaraEntriesExpanded = false;
  fsiCalcExpanded = false;

  // Shareholder management
  shareholders: Shareholder[] = [];
  numberOfPartners = '';
  constructionSchedExpanded = false;
  groupEntitiesExpanded = false;
  completedProjectsExpanded = false;
  unsoldStockExpanded = false;
  landBankExpanded = false;
  projectsFactsheetExpanded = false;
  salesMisExpanded = false;
  salesPlanExpanded = false;
  compiledCostExpanded = false;
  debtDetailsExpanded = false;
  googleMapExpanded = false;

  // Global flag to control whether table row clicks should open popups
  enableTableRowClickPopups = false;

  // Helper method to check if row click popups are enabled
  shouldOpenPopupOnRowClick(): boolean {
    return this.enableTableRowClickPopups;
  }

  // Toggle Proposal accordion
  toggleProposal(): void {
    this.proposalExpanded = !this.proposalExpanded;
  }

  // Toggle Initial KYC accordion
  toggleInitialKyc(): void {
    this.initialKycExpanded = !this.initialKycExpanded;
  }

  // Shareholder management methods
  addShareholder(): void {
    const newShareholder: Shareholder = {
      id: this.shareholders.length + 1,
      name: '',
      shareholdingPercentage: '',
      profitSharingRatio: '',
      asPerPdDate: '',
      pan: '',
      aadharNo: '',
      dateOfBirth: '',
      functionsHandled: '',
      yearsInRealEstate: '',
      yearsInGroup: '',
      fatherName: '',
      motherName: '',
      motherMaidenName: '',
      spouseName: '',
      childrenNames: '',
      contactNumber: '',
      emailId: '',
      residentialAddress: '',
      latestNetWorth: '',
      detailedProfile: ''
    };
    this.shareholders.push(newShareholder);
  }

  removeShareholder(index: number): void {
    if (this.shareholders.length > 1) {
      this.shareholders.splice(index, 1);
      // Re-index the remaining shareholders
      this.shareholders.forEach((shareholder, i) => {
        shareholder.id = i + 1;
      });
    }
  }

  // Initialize with one shareholder by default
  initializeShareholders(): void {
    if (this.shareholders.length === 0) {
      this.addShareholder();
    }
  }

  // Validate shareholder data
  validateShareholderData(): boolean {
    for (let i = 0; i < this.shareholders.length; i++) {
      const shareholder = this.shareholders[i];

      if (!shareholder.name.trim()) {
        alert(`Please enter name for Shareholder ${i + 1}`);
        return false;
      }

      if (!shareholder.pan.trim()) {
        alert(`Please enter PAN for Shareholder ${i + 1}`);
        return false;
      }

      if (!shareholder.contactNumber.trim()) {
        alert(`Please enter contact number for Shareholder ${i + 1}`);
        return false;
      }

      // Validate PAN format (basic validation)
      const panRegex = /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/;
      if (!panRegex.test(shareholder.pan.toUpperCase())) {
        alert(`Please enter a valid PAN for Shareholder ${i + 1} (Format: **********)`);
        return false;
      }

      // Validate contact number (basic validation)
      const phoneRegex = /^[0-9]{10}$/;
      if (!phoneRegex.test(shareholder.contactNumber.replace(/\D/g, ''))) {
        alert(`Please enter a valid 10-digit contact number for Shareholder ${i + 1}`);
        return false;
      }
    }

    return true;
  }

  // Save Initial KYC data
  saveInitialKyc(): void {
    if (!this.numberOfPartners.trim()) {
      alert('Please enter the number of partners/directors');
      return;
    }

    if (!this.validateShareholderData()) {
      return;
    }

    // Here you would typically save the data to a service
    console.log('Initial KYC Data:', {
      numberOfPartners: this.numberOfPartners,
      shareholders: this.shareholders
    });

    alert('Initial KYC data saved successfully!');
  }

  // Toggle Borrowers Details accordion
  toggleBorrowersDetails(): void {
    this.borrowersDetailsExpanded = !this.borrowersDetailsExpanded;
  }

  // Toggle Other Business Details accordion
  toggleOtherBusinessDetails(): void {
    this.otherBusinessDetailsExpanded = !this.otherBusinessDetailsExpanded;
  }

  // Toggle Project Land Details accordion
  toggleProjectLandDetails(): void {
    this.projectLandDetailsExpanded = !this.projectLandDetailsExpanded;
  }

  // Toggle Sathbara & Mutation Entries accordion
  toggleSathbaraEntries(): void {
    this.sathbaraEntriesExpanded = !this.sathbaraEntriesExpanded;
  }

  // Toggle FSI Calc & Approval Status accordion
  toggleFsiCalc(): void {
    this.fsiCalcExpanded = !this.fsiCalcExpanded;
  }

  // Toggle Construction & Payment Sched accordion
  toggleConstructionSched(): void {
    this.constructionSchedExpanded = !this.constructionSchedExpanded;
  }

  // Toggle Group Entities accordion
  toggleGroupEntities(): void {
    this.groupEntitiesExpanded = !this.groupEntitiesExpanded;
  }

  // Toggle Completed Projects accordion
  toggleCompletedProjects(): void {
    this.completedProjectsExpanded = !this.completedProjectsExpanded;
  }

  // Toggle Unsold & Leased Prop accordion
  toggleUnsoldStock(): void {
    this.unsoldStockExpanded = !this.unsoldStockExpanded;
  }

  // Toggle Land Bank & Upcoming Projects accordion
  toggleLandBank(): void {
    this.landBankExpanded = !this.landBankExpanded;
  }

  // Toggle Project Factsheet accordion
  toggleProjectsFactsheet(): void {
    this.projectsFactsheetExpanded = !this.projectsFactsheetExpanded;
  }

  // Toggle Sales MIS & Inventory accordion
  toggleSalesMis(): void {
    this.salesMisExpanded = !this.salesMisExpanded;
  }

  // Toggle Sales Plan accordion
  toggleSalesPlan(): void {
    this.salesPlanExpanded = !this.salesPlanExpanded;
  }

  // Toggle Compiled Cost accordion
  toggleCompiledCost(): void {
    this.compiledCostExpanded = !this.compiledCostExpanded;
  }

  // Toggle Debt Details accordion
  toggleDebtDetails(): void {
    this.debtDetailsExpanded = !this.debtDetailsExpanded;
  }

  // Toggle Google Map accordion
  toggleGoogleMap(): void {
    this.googleMapExpanded = !this.googleMapExpanded;
  }

  // Helper method to get documents by section
  getDocumentsBySection(sectionId: number): any[] {
    // If filteredDocuments is populated, use it; otherwise use all documents
    const docsToFilter = this.filteredDocuments.length > 0 ? this.filteredDocuments : this.documents;
    return docsToFilter.filter(doc => doc.sectionId === sectionId);
  }

  // Document status tracking
  documentStatus: { [key: number]: string } = {};

  // Follow-up data tracking - now an array of follow-ups for each document
  followUpData: { [key: number]: Array<{ date: string, time: string, notes: string, timestamp: number }> } = {};

  // Track if follow-up form is visible
  followUpFormVisible: { [key: number]: boolean } = {};

  // Section-level follow-up form fields
  sectionFollowUpDate: string = '';
  sectionFollowUpTime: string = '';
  sectionFollowUpNotes: string = '';

  // NGX Datatable properties
  @ViewChild('documentTable') documentTable: DatatableComponent;
  @ViewChild('salesPlanTable') salesPlanTable: DatatableComponent;
  @ViewChild('debtDetailsTable') debtDetailsTable: DatatableComponent;
  @ViewChild('otherBusinessTable') otherBusinessTable: DatatableComponent;
  @ViewChild('groupEntitiesTable') groupEntitiesTable: DatatableComponent;
  @ViewChild('sathbaraEntriesTable') sathbaraEntriesTable: DatatableComponent;
  @ViewChild('completedProjectsTable') completedProjectsTable: DatatableComponent;
  @ViewChild('ongoingProjectsTable') ongoingProjectsTable: DatatableComponent;
  @ViewChild('unsoldStockTable') unsoldStockTable: DatatableComponent;
  @ViewChild('leasedPropertiesTable') leasedPropertiesTable: DatatableComponent;
  @ViewChild('compiledCostTable') compiledCostTable: DatatableComponent;
  @ViewChild('fundInfusionTable') fundInfusionTable: DatatableComponent;
  @ViewChild('landBankTable') landBankTable: DatatableComponent;
  @ViewChild('landBankDetailTable') landBankDetailTable: DatatableComponent;
  @ViewChild('projectLandDetailTable') projectLandDetailTable: DatatableComponent;
  ColumnMode = ColumnMode;
  SelectionType = SelectionType;

  // Document sections
  documentSections = [
    { id: 2, name: 'Project Master File / APF File (Legal File)' },
    { id: 3, name: 'Company Documents' },
    { id: 4, name: 'Other Documents & Details' }
  ];

  // Document search term
  documentSearchTerm: string = '';

  // Filtered documents
  filteredDocuments: any[] = [];

  // Document data for ngx-datatable
  documents = [
    // Project Master File / APF File (Legal File)
    { id: 1, sectionId: 2, name: 'Draft Agreement (Agreement for Sale) as per RERA', checked: false, status: '', date: '2023-08-15', time: '10:30', notes: '', explainDate: '', receivedDate: '' },
    { id: 2, sectionId: 2, name: 'N.A. Order with N.A.Tax Receipt / Vinishchiti Patra (Reconfirmation Certificate)', checked: false, status: '', date: '2023-08-16', time: '14:15', notes: '' },
    { id: 3, sectionId: 2, name: '7/12 with all Mutation Entries / Assesment Sheet / Permanent Record (PR) Copy / Property Record (PR) Copy', checked: false, status: '', date: '2023-08-17', time: '09:45', notes: '' },
    { id: 4, sectionId: 2, name: 'Title Certificate', checked: false, status: '', date: '2023-08-14', time: '11:00', notes: '' },
    { id: 5, sectionId: 2, name: '30 years\' Updated Search Report', checked: false, status: '', date: '2023-08-18', time: '15:30', notes: '' },
    { id: 6, sectionId: 2, name: 'Brochure', checked: false, status: '', date: '2023-08-19', time: '10:00', notes: '' },
    { id: 7, sectionId: 2, name: 'Development Agreement / Purchase Agreement (Kharedi Khat) / Conveyance Deed / Sathe Karaar', checked: false, status: '', date: '2023-08-20', time: '11:45', notes: '' },
    { id: 8, sectionId: 2, name: 'Chain of Agreements (Applicable only in cases of Purchase)', checked: false, status: '', date: '2023-08-21', time: '14:00', notes: '' },
    { id: 9, sectionId: 2, name: 'Power of Attorney', checked: false, status: '', date: '2023-08-22', time: '09:30', notes: '' },
    { id: 10, sectionId: 2, name: 'C.C. (Commencement Certificate) with Approved Plan Copy', checked: false, status: '', date: '2023-08-23', time: '16:15', notes: '' },
    { id: '11', sectionId: 2, name: 'MahaRERA Certificates:', checked: false, status: '', date: '2023-08-24', time: '10:30', notes: '' },
    { id: '11.a', sectionId: 2, name: 'MahaRERA Registration Certificate', checked: false, status: '', date: '2023-08-25', time: '11:30', notes: '' },
    { id: '11.b', sectionId: 2, name: 'Architect\'s Certificate (Form 1)', checked: false, status: '', date: '2023-08-26', time: '12:30', notes: '' },
    { id: '11.c', sectionId: 2, name: 'Engineer\'s Certificate (Form 2)', checked: false, status: '', date: '2023-08-27', time: '13:30', notes: '' },
    { id: '11.d', sectionId: 2, name: 'CA\'s Certificate (Form 3)', checked: false, status: '', date: '2023-08-28', time: '14:30', notes: '' },
    { id: 12, sectionId: 2, name: 'Fire NOC Application / Certificate', checked: false, status: '', date: '2023-08-29', time: '15:30', notes: '' },
    { id: 13, sectionId: 2, name: 'NA Assessment Bill and Paid Receipt/ Property Tax Bill and Paid Receipt', checked: false, status: '', date: '2023-08-30', time: '16:30', notes: '' },

    // Company Documents
    { id: 1, sectionId: 3, name: 'Company PAN Card', checked: false, status: '', date: '2023-08-31', time: '16:15', notes: '' },
    { id: '1.1', sectionId: 3, name: 'Company\'s GST Registration Certificates & Udyam Certificate', checked: false, status: '', date: '2023-09-01', time: '10:30', notes: '' },
    { id: 2, sectionId: 3, name: 'Partnership Deed', checked: false, status: '', date: '2023-09-02', time: '11:30', notes: '' },
    { id: '2.1', sectionId: 3, name: 'ROF Certificate of the firm', checked: false, status: '', date: '2023-09-03', time: '12:30', notes: '' },
    { id: 3, sectionId: 3, name: 'All Partners\' KYC Documents (PAN Card, Aadhar Card) & 2 Photos (3 Sets)', checked: false, status: '', date: '2023-09-04', time: '13:30', notes: '' },
    { id: 4, sectionId: 3, name: 'All Partners\' last 3 years\' ITR copies', checked: false, status: '', date: '2023-09-05', time: '14:30', notes: '' },
    { id: 5, sectionId: 3, name: 'Company\'s last 3 Years\' ITR Copies or Current year\'s provisional (P&L, Balance sheet & Computation)', checked: false, status: '', date: '2023-09-06', time: '09:30', notes: '' },
    { id: 6, sectionId: 3, name: 'Net Worth Statements of All Partners', checked: false, status: '', date: '2023-09-07', time: '10:30', notes: '' },
    { id: 7, sectionId: 3, name: '1 Year\'s Updated Bank Statement (Current A/c ) of the Company & Partners', checked: false, status: '', date: '2023-09-08', time: '11:30', notes: '' },
    { id: 8, sectionId: 3, name: 'Profile of All Partners', checked: false, status: '', date: '2023-09-09', time: '12:30', notes: '' },
    { id: 9, sectionId: 3, name: 'Loan Details of All Partners (Sanction letter and latest 1 year\'s loan account statements)', checked: false, status: '', date: '2023-09-10', time: '13:30', notes: '' },

    // Other Documents & Details
    { id: 1, sectionId: 4, name: 'Sales Booking MIS (As per our format)', checked: false, status: '', date: '2023-09-11', time: '11:30', notes: '' },
    { id: 2, sectionId: 4, name: 'Completed Projects\' OC / CC with Partnership Deed', checked: false, status: '', date: '2023-09-12', time: '12:30', notes: '' },
    { id: 3, sectionId: 4, name: 'All Partners\' Contact Numbers', checked: false, status: '', date: '2023-09-13', time: '13:30', notes: '' },
    { id: 4, sectionId: 4, name: 'APF Letters', checked: false, status: '', date: '2023-09-14', time: '14:30', notes: '' }
  ];

  // Institute selection tracking
  selectedGovtBanks: number[] = [];
  selectedPrivateBanks: number[] = [];
  selectedNBFCs: number[] = [];
  selectedConsultancies: number[] = [];
  selectedFundHouses: number[] = [];

  // Track if institute form has been submitted
  instituteFormSubmitted: boolean = false;

  // Data explanation properties
  dataExplanation: string = '';
  dataExplanationStatus: string = '';
  dataExplanationNotes: string = '';
  dataExplanationFollowUpDate: string = '';
  dataExplanationFollowUpTime: string = '';

  // Data explanation follow-up history
  dataExplanationFollowUps: Array<{
    date: string,
    time: string,
    notes: string,
    status: string,
    timestamp: number
  }> = [];

  // Borrower's Details properties
  borrowerDetails = {
    name: 'SB Corporation',
    constitution: '',
    partnersCount: null,
    about: '',
    gstNo: '',
    cinNo: '',
    incorporationDate: '',
    pan: '',
    registeredOffice: '',
    corporateOffice: '',
    email: '',
    website: '',
    experience: '',
    defaultHistory: '',
    pep: '',
    legalCases: '',
    teamSize: {
      managerial: null,
      assistantManagers: null,
      accountsDept: null,
      adminDept: null,
      salesDept: null,
      engineers: null,
      supervisors: null
    }
  };

  // Background of the Group properties
  groupBackground = {
    name: '',
    yearVentured: '',
    mainPromoter: '',
    localities: '',
    completedProjects: {
      count: 0,
      area: 1275
    },
    ongoingProjects: {
      count: 10,
      area: 120000
    },
    upcomingProjects: {
      count: 10,
      area: *********
    }
  };

  // Track bank statuses and follow-ups
  bankStatuses: { [key: string]: {
    dataSent: string,
    dataExplained: string,
    inPrinciple: string,
    followUpDate: string,
    followUpTime: string,
    notes: string,
    action: string
  } } = {};

  // Bank lists
  govtBanks = [
    { id: 1, name: 'State Bank of India [SBI]' },
    { id: 2, name: 'Bank of India [BOI]' },
    { id: 3, name: 'Life Insurance Corp of India [LIC]' },
    { id: 4, name: 'Union Bank Of India' },
    { id: 5, name: 'Bank of Baroda' },
    { id: 6, name: 'Canara Bank' },
    { id: 7, name: 'Central Bank of India' },
    { id: 8, name: 'Punjab National Bank' }
  ];

  privateBanks = [
    { id: 1, name: 'DCB Bank' },
    { id: 2, name: 'AU Small Fin Bank Ltd' },
    { id: 3, name: 'ICICI Bank' },
    { id: 4, name: 'Axis Bank' },
    { id: 5, name: 'Kotak Mahindra Bank' },
    { id: 6, name: 'Bandhan Bank' },
    { id: 7, name: 'IDBI Bank' },
    { id: 8, name: 'Yes Bank' },
    { id: 9, name: 'Federal Bank' },
    { id: 10, name: 'Indusind bank' }
  ];

  nbfcs = [
    { id: 1, name: 'Aditya Birla Finance Ltd [ABFL]' },
    { id: 2, name: 'Aditya Birla Hsg Finance Ltd [ABHFL]' },
    { id: 3, name: 'Capri Global Capital Ltd [CGCL]' },
    { id: 4, name: 'ICICI Hsg Fin Co [ICICI HFC]' },
    { id: 5, name: 'Bajaj Hsg Fin Ltd [BHFL]' },
    { id: 6, name: 'Piramal Capital' },
    { id: 7, name: 'SMFG Grihashakti [Fullerton India Credit Co Ltd - FICCL]' },
    { id: 8, name: 'India Infoline Fin Ltd [IIFL]' },
    { id: 9, name: 'Shriram Hsg Fin Ltd [SHFL]' },
    { id: 10, name: 'Hero Hsg Fin Ltd [Hero HFL]' },
    { id: 11, name: 'Hero FinCorp Ltd' },
    { id: 12, name: 'A K Capital' },
    { id: 13, name: 'Arka Fincap Ltd' },
    { id: 14, name: 'Reliance Nippon MF' },
    { id: 15, name: 'ASK Finance' },
    { id: 16, name: 'Axis Bank (HFC)' }
  ];

  // Corporate Consultancy list
  consultancies = [
    { id: 1, name: 'Ernst & Young (EY)' },
    { id: 2, name: 'Deloitte' },
    { id: 3, name: 'KPMG' },
    { id: 4, name: 'PricewaterhouseCoopers (PwC)' },
    { id: 5, name: 'McKinsey & Company' },
    { id: 6, name: 'Boston Consulting Group (BCG)' },
    { id: 7, name: 'Bain & Company' },
    { id: 8, name: 'Accenture' },
    { id: 9, name: 'Grant Thornton' },
    { id: 10, name: 'BDO' }
  ];

  // Fund Houses list
  fundHouses = [
    { id: 1, name: 'SBI Mutual Fund' },
    { id: 2, name: 'HDFC Mutual Fund' },
    { id: 3, name: 'ICICI Prudential Mutual Fund' },
    { id: 4, name: 'Aditya Birla Sun Life Mutual Fund' },
    { id: 5, name: 'Nippon India Mutual Fund' },
    { id: 6, name: 'Kotak Mahindra Mutual Fund' },
    { id: 7, name: 'Axis Mutual Fund' },
    { id: 8, name: 'UTI Mutual Fund' },
    { id: 9, name: 'DSP Mutual Fund' },
    { id: 10, name: 'IDFC Mutual Fund' },
    { id: 11, name: 'Tata Mutual Fund' },
    { id: 12, name: 'Franklin Templeton Mutual Fund' }
  ];

  // Email compose properties
  peoples = PeoplesData.peoples;
  selectedTo: any[] = [];
  selectedCc: any[] = [];
  messageValue: string = `Dear Sir,
<br><br>
This is regarding the loan facility proposal for your project _____________.
<br><br>
After presenting the proposal details for prima facie review to our choice lenders, we have received the following indicative terms from the lender:
<br><br>
Lender: (Bank / NBFC Name)
<br><br>
Borrower: (Company Name)
<br>
Co-borrowers: (Promoters' Names)
<br><br>
Summary of Terms & Conditions:
<br>
Facility: CF / PF / IF
<br>
Loan Amt: Rs ____ Crs
<br>
Tenure: ___ years
<br>
ROI: __% pa
<br>
Fee & Other Charges: ____% of the loan amount + Applicable GST & ____% will include keyman Insurance & <br> IHO cards +Applicable GST.
<br>
Initial Login fees of Rs. ___ Lacs + GST for legal & technical due diligence.
<br>
End Use: To be used towards construction finance & approvals for the project "____________" + Transaction Expenses + ISRA


<br><br>
We hereby request you to study these details and give us your availability for a call to discuss the further plan of action.


<br><br><br>
-        Regards,

For Biz Corp Solutions`;

  // Quill editor configuration
  quillConfig = {
    toolbar: {
      container: [
        ['bold', 'italic', 'underline', 'strike'],        // toggled buttons
        [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
        [{ 'list': 'ordered'}, { 'list': 'bullet' }],
        [{ 'indent': '-1'}, { 'indent': '+1' }],          // outdent/indent
        // [{ 'direction': 'rtl' }],                      // text direction
        [{ 'align': [] }],
        ['link', 'image', 'video']
      ],
    },
  };

  // Sales MIS & Inventory Summary data for ngx-datatable with dummy data
  salesMisSummaryRows: SalesMisSummaryRow[] = [
    {
      id: 1,
      saleableBuiltUpArea: 25000,
      mahareraCarpetAreaSqMt: 18500,
      carpetAreaSqFt: 199132,
      numberOfFlats: 120,
      numberOfShops: 15,
      numberOfOffices: 8,
      refugeUnits: 4,
      achievedForSoldArea: 150000,
      targetForUnsoldArea: 85000
    },
    {
      id: 2,
      saleableBuiltUpArea: 15000,
      mahareraCarpetAreaSqMt: 11200,
      carpetAreaSqFt: 120556,
      numberOfFlats: 75,
      numberOfShops: 10,
      numberOfOffices: 5,
      refugeUnits: 2,
      achievedForSoldArea: 95000,
      targetForUnsoldArea: 65000
    }
  ];

  // Sales MIS & Inventory data for ngx-datatable with dummy data
  salesMisRows: SalesMisRow[] = [
    {
      id: 1,
      buildingWingName: 'Wing A',
      floorNo: '1',
      unitNo: '101',
      mahareraCarpetArea: 650,
      carpetArea: 625,
      saleableBuiltUpArea: 950,
      unitType: 'RESI',
      ownerType: 'DEVELOPER',
      undividedShareOfLand: 125,
      extraCarpetAreaSoldToExistingTenants: 0,
      extraSaleableAreaSoldToExistingTenants: 0,
      saleStatus: 'SOLD',
      buyerName: 'Rahul Sharma',
      contactNo: '9876543210',
      emailId: '<EMAIL>',
      dateOfBooking: '10-Jan-2023',
      dateOfRegistration: '15-Feb-2023',
      registrationNo: 'REG/2023/001',
      basicCost: 8500000,
      developmentCostComponents: 1500000,
      totalValueOfUnit: ********,
      demandRaised: ********,
      amountReceived: 8000000,
      balance: 2000000,
      stageOfConstruction: 'Slab work in progress',
      collections: 8000000,
      homeLoanFinancierName: 'HDFC Home Loans',
      nocIssuedByEarlierFinancier: 'N/A',
      typeOfCustomer: 'Salaried'
    },
    {
      id: 2,
      buildingWingName: 'Wing A',
      floorNo: '1',
      unitNo: '102',
      mahareraCarpetArea: 750,
      carpetArea: 725,
      saleableBuiltUpArea: 1100,
      unitType: 'RESI',
      ownerType: 'DEVELOPER',
      undividedShareOfLand: 145,
      extraCarpetAreaSoldToExistingTenants: 0,
      extraSaleableAreaSoldToExistingTenants: 0,
      saleStatus: 'UNSOLD',
      buyerName: '',
      contactNo: '',
      emailId: '',
      dateOfBooking: '',
      dateOfRegistration: '',
      registrationNo: '',
      basicCost: 9500000,
      developmentCostComponents: 1700000,
      totalValueOfUnit: 11200000,
      demandRaised: 0,
      amountReceived: 0,
      balance: 0,
      stageOfConstruction: 'Slab work in progress',
      collections: 0,
      homeLoanFinancierName: '',
      nocIssuedByEarlierFinancier: '',
      typeOfCustomer: ''
    },
    {
      id: 3,
      buildingWingName: 'Wing A',
      floorNo: '2',
      unitNo: '201',
      mahareraCarpetArea: 650,
      carpetArea: 625,
      saleableBuiltUpArea: 950,
      unitType: 'RESI',
      ownerType: 'DEVELOPER',
      undividedShareOfLand: 125,
      extraCarpetAreaSoldToExistingTenants: 0,
      extraSaleableAreaSoldToExistingTenants: 0,
      saleStatus: 'SOLD',
      buyerName: 'Priya Patel',
      contactNo: '8765432109',
      emailId: '<EMAIL>',
      dateOfBooking: '15-Feb-2023',
      dateOfRegistration: '20-Mar-2023',
      registrationNo: 'REG/2023/002',
      basicCost: 8500000,
      developmentCostComponents: 1500000,
      totalValueOfUnit: ********,
      demandRaised: ********,
      amountReceived: 7500000,
      balance: 2500000,
      stageOfConstruction: 'Structure completed',
      collections: 7500000,
      homeLoanFinancierName: 'ICICI Home Loans',
      nocIssuedByEarlierFinancier: 'N/A',
      typeOfCustomer: 'Self-employed'
    },
    {
      id: 4,
      buildingWingName: 'Wing A',
      floorNo: '2',
      unitNo: '202',
      mahareraCarpetArea: 750,
      carpetArea: 725,
      saleableBuiltUpArea: 1100,
      unitType: 'RESI',
      ownerType: 'DEVELOPER',
      undividedShareOfLand: 145,
      extraCarpetAreaSoldToExistingTenants: 0,
      extraSaleableAreaSoldToExistingTenants: 0,
      saleStatus: 'MORTGAGED',
      buyerName: 'Vikram Singh',
      contactNo: '7654321098',
      emailId: '<EMAIL>',
      dateOfBooking: '05-Mar-2023',
      dateOfRegistration: '10-Apr-2023',
      registrationNo: 'REG/2023/003',
      basicCost: 9000000,
      developmentCostComponents: 1600000,
      totalValueOfUnit: ********,
      demandRaised: ********,
      amountReceived: 5300000,
      balance: 5300000,
      stageOfConstruction: 'Internal work in progress',
      collections: 5300000,
      homeLoanFinancierName: 'SBI Home Loans',
      nocIssuedByEarlierFinancier: 'Yes - Axis Bank',
      typeOfCustomer: 'Salaried'
    },
    {
      id: 5,
      buildingWingName: 'Wing B',
      floorNo: '1',
      unitNo: '101',
      mahareraCarpetArea: 675,
      carpetArea: 650,
      saleableBuiltUpArea: 975,
      unitType: 'COMM',
      ownerType: 'LANDOWNER',
      undividedShareOfLand: 130,
      extraCarpetAreaSoldToExistingTenants: 50,
      extraSaleableAreaSoldToExistingTenants: 75,
      saleStatus: 'SOLD',
      buyerName: 'Amit Verma',
      contactNo: '**********',
      emailId: '<EMAIL>',
      dateOfBooking: '20-Mar-2023',
      dateOfRegistration: '25-Apr-2023',
      registrationNo: 'REG/2023/004',
      basicCost: ********,
      developmentCostComponents: 1800000,
      totalValueOfUnit: ********,
      demandRaised: ********,
      amountReceived: 9000000,
      balance: 2800000,
      stageOfConstruction: 'Structure completed',
      collections: 9000000,
      homeLoanFinancierName: 'Kotak Home Loans',
      nocIssuedByEarlierFinancier: 'N/A',
      typeOfCustomer: 'Business Owner'
    },
    {
      id: 6,
      buildingWingName: 'Wing B',
      floorNo: '1',
      unitNo: '102',
      mahareraCarpetArea: 775,
      carpetArea: 750,
      saleableBuiltUpArea: 1125,
      unitType: 'COMM',
      ownerType: 'LANDOWNER',
      undividedShareOfLand: 150,
      extraCarpetAreaSoldToExistingTenants: 0,
      extraSaleableAreaSoldToExistingTenants: 0,
      saleStatus: 'UNSOLD',
      buyerName: '',
      contactNo: '',
      emailId: '',
      dateOfBooking: '',
      dateOfRegistration: '',
      registrationNo: '',
      basicCost: 11000000,
      developmentCostComponents: 2000000,
      totalValueOfUnit: 13000000,
      demandRaised: 0,
      amountReceived: 0,
      balance: 0,
      stageOfConstruction: 'Foundation work completed',
      collections: 0,
      homeLoanFinancierName: '',
      nocIssuedByEarlierFinancier: '',
      typeOfCustomer: ''
    },
    {
      id: 7,
      buildingWingName: 'Wing B',
      floorNo: '2',
      unitNo: '201',
      mahareraCarpetArea: 675,
      carpetArea: 650,
      saleableBuiltUpArea: 975,
      unitType: 'COMM',
      ownerType: 'TENANT',
      undividedShareOfLand: 130,
      extraCarpetAreaSoldToExistingTenants: 75,
      extraSaleableAreaSoldToExistingTenants: 100,
      saleStatus: 'SOLD',
      buyerName: 'Neha Gupta',
      contactNo: '5432109876',
      emailId: '<EMAIL>',
      dateOfBooking: '10-Apr-2023',
      dateOfRegistration: '15-May-2023',
      registrationNo: 'REG/2023/005',
      basicCost: 9800000,
      developmentCostComponents: 1700000,
      totalValueOfUnit: 11500000,
      demandRaised: 11500000,
      amountReceived: 8000000,
      balance: 3500000,
      stageOfConstruction: 'Internal work in progress',
      collections: 8000000,
      homeLoanFinancierName: 'LIC Housing Finance',
      nocIssuedByEarlierFinancier: 'N/A',
      typeOfCustomer: 'Self-employed'
    },
    {
      id: 8,
      buildingWingName: 'Wing B',
      floorNo: '2',
      unitNo: '202',
      mahareraCarpetArea: 775,
      carpetArea: 750,
      saleableBuiltUpArea: 1125,
      unitType: 'COMM',
      ownerType: 'TENANT',
      undividedShareOfLand: 150,
      extraCarpetAreaSoldToExistingTenants: 60,
      extraSaleableAreaSoldToExistingTenants: 85,
      saleStatus: 'MORTGAGED',
      buyerName: 'Rajesh Kumar',
      contactNo: '4321098765',
      emailId: '<EMAIL>',
      dateOfBooking: '25-Apr-2023',
      dateOfRegistration: '30-May-2023',
      registrationNo: 'REG/2023/006',
      basicCost: ********,
      developmentCostComponents: 1900000,
      totalValueOfUnit: ********,
      demandRaised: ********,
      amountReceived: 6200000,
      balance: 6200000,
      stageOfConstruction: 'Foundation work completed',
      collections: 6200000,
      homeLoanFinancierName: 'Bajaj Housing Finance',
      nocIssuedByEarlierFinancier: 'Yes - HDFC Bank',
      typeOfCustomer: 'Business Owner'
    }
  ];

  // Sales Plan data for ngx-datatable with dummy data
  salesPlanRows: SalesPlanRow[] = [
    { id: 1, particulars: 'No. of flats to be booked / sold', q1: 12, q2: 15, q3: 18, q4: 20, q5: 22, q6: 25, q7: 28, q8: 30, q9: 32 },
    { id: 2, particulars: 'Average area of the flat', q1: 850, q2: 875, q3: 900, q4: 925, q5: 950, q6: 975, q7: 1000, q8: 1025, q9: 1050 },
    { id: 3, particulars: 'Rate per sq. ft.', q1: 4500, q2: 4600, q3: 4700, q4: 4800, q5: 4900, q6: 5000, q7: 5100, q8: 5200, q9: 5300 },
    { id: 4, particulars: 'Sale value', q1: ********, q2: ********, q3: ********, q4: ********, q5: *********, q6: *********, q7: *********, q8: *********, q9: ********* }
  ];

  // Other Business Details data for ngx-datatable with dummy data
  otherBusinessRows: OtherBusinessRow[] = [
    {
      id: 1,
      promoterName: 'Rajesh Sharma',
      companyName: 'Sunrise Retail Solutions Pvt Ltd',
      constitution: 'Private Limited',
      dateOfIncorporation: '10-Mar-2018',
      pan: '**********',
      cinGstNo: 'U72200MH2018PTC123456',
      location: 'Shop No. 12, Andheri West, Mumbai, Maharashtra - 400053',
      lineOfActivity: 'Retail Chain & Distribution',
      yearsOfExperience: 5,
      avgAnnualTurnover: 7500000,
      avgAnnualProfit: 18.5
    },
    {
      id: 2,
      promoterName: 'Priya Mehta & Vikram Mehta',
      companyName: 'Green Earth Exports LLP',
      constitution: 'LLP',
      dateOfIncorporation: '22-Jun-2019',
      pan: '**********',
      cinGstNo: 'AAF-1234',
      location: 'Plot 45, MIDC Industrial Area, Pune, Maharashtra - 411057',
      lineOfActivity: 'Export of Organic Products',
      yearsOfExperience: 4,
      avgAnnualTurnover: 12000000,
      avgAnnualProfit: 22.3
    },
    {
      id: 3,
      promoterName: 'Arun Kumar',
      companyName: 'Tech Innovations Lab India Pvt Ltd',
      constitution: 'Private Limited',
      dateOfIncorporation: '05-Jan-2020',
      pan: '**********',
      cinGstNo: 'U72900KA2020PTC098765',
      location: 'No. 23, Tech Park, Whitefield, Bangalore, Karnataka - 560066',
      lineOfActivity: 'Software Development & IT Services',
      yearsOfExperience: 3,
      avgAnnualTurnover: 9000000,
      avgAnnualProfit: 25.7
    }
  ];

  // Group Entities data for ngx-datatable with dummy data
  groupEntitiesRows: GroupEntityRow[] = [
    {
      id: 1,
      companyName: 'ABC Developers Pvt Ltd',
      constitution: 'Private Limited',
      dateOfIncorporation: '15-Apr-2010',
      pan: '**********',
      cinGstNo: 'U45200MH2010PTC123456',
      registeredOfficeAddress: '501, Prestige Tower, Andheri East, Mumbai, Maharashtra - 400069',
      partnersDirectors: 'Rajesh Kumar, Amit Shah, Priya Patel',
      profitSharingRatio: '40:30:30',
      din: 'DIN12345678, DIN87654321, DIN23456789',
      currentProjects: 'Sunrise Heights, Green Valley'
    },
    {
      id: 2,
      companyName: 'ABC Infra Solutions LLP',
      constitution: 'LLP',
      dateOfIncorporation: '22-Aug-2015',
      pan: '**********',
      cinGstNo: 'AAF-5678',
      registeredOfficeAddress: '502, Prestige Tower, Andheri East, Mumbai, Maharashtra - 400069',
      partnersDirectors: 'Vikram Mehta, Sunil Joshi',
      profitSharingRatio: '60:40',
      din: 'N/A',
      currentProjects: 'Metro Line Extension, Highway Development'
    },
    {
      id: 3,
      companyName: 'ABC Commercial Properties Pvt Ltd',
      constitution: 'Private Limited',
      dateOfIncorporation: '10-Jan-2018',
      pan: '**********',
      cinGstNo: 'U70100MH2018PTC987654',
      registeredOfficeAddress: '503, Prestige Tower, Andheri East, Mumbai, Maharashtra - 400069',
      partnersDirectors: 'Rajesh Kumar, Neha Singh, Arun Patel',
      profitSharingRatio: '50:25:25',
      din: 'DIN12345678, DIN34567890, DIN45678901',
      currentProjects: 'Business Park Phase 1, Tech Hub Tower'
    },
    {
      id: 4,
      companyName: 'ABC Hospitality Services Pvt Ltd',
      constitution: 'Private Limited',
      dateOfIncorporation: '05-Mar-2019',
      pan: '**********',
      cinGstNo: 'U55100MH2019PTC654321',
      registeredOfficeAddress: '504, Prestige Tower, Andheri East, Mumbai, Maharashtra - 400069',
      partnersDirectors: 'Rajesh Kumar, Sanjay Gupta',
      profitSharingRatio: '50:50',
      din: 'DIN12345678, DIN56789012',
      currentProjects: 'Luxury Hotel & Resort, Business Convention Center'
    }
  ];

  // Sathbara & Mutation Entries data for ngx-datatable with dummy data
  sathbaraEntryRows: SathbaraEntryRow[] = [
    {
      id: 1,
      surveyNumber: '123/4/A',
      area: '5000 Sq. Mt.',
      mutationEntries: 'Entry No. 1234 dated 15-Jun-2022',
      landOwnerName: 'Rajesh Kumar Sharma',
      remarks: 'Mutation completed and registered'
    },
    {
      id: 2,
      surveyNumber: '456/7/B',
      area: '7500 Sq. Mt.',
      mutationEntries: 'Entry No. 6789 dated 22-Jul-2022',
      landOwnerName: 'Sunil Patel',
      remarks: 'Mutation application submitted, awaiting approval'
    },
    {
      id: 3,
      surveyNumber: '789/10/C',
      area: '3200 Sq. Mt.',
      mutationEntries: 'Entry No. 5432 dated 05-Aug-2022',
      landOwnerName: 'Priya Desai',
      remarks: 'Mutation completed with NA conversion'
    },
    {
      id: 4,
      surveyNumber: '234/5/D',
      area: '10000 Sq. Mt.',
      mutationEntries: 'Entry No. 9876 dated 15-Sep-2022',
      landOwnerName: 'Amit Joshi',
      remarks: 'Mutation in process, verification pending'
    },
    {
      id: 5,
      surveyNumber: '567/8/E',
      area: '8500 Sq. Mt.',
      mutationEntries: 'Entry No. 1357 dated 30-Oct-2022',
      landOwnerName: 'Neha Singh',
      remarks: 'Mutation rejected due to incomplete documentation'
    }
  ];

  // Ongoing Projects data for ngx-datatable with dummy data
  ongoingProjectRows: OngoingProjectRow[] = [
    {
      id: 1,
      projectName: 'Skyline Towers',
      entityName: 'ABC Developers Pvt Ltd',
      promotersNames: 'Rajesh Kumar, Amit Shah',
      projectStructure: 'G+14 Tower with 2 Basement',
      constructionApprovals: 'CC up to 14th Floor',
      latestCCDate: '15-Jun-2022',
      projectCategory: 'NORMAL LAYOUT',
      ownershipType: 'OWNED',
      plotArea: '5,000 sq.mt',
      category: 'Residential',
      sharingDetails: '60:40',
      area1: 150000,
      units1: 120,
      area2: 50000,
      units2: 40,
      area3: 20000,
      units3: 20,
      soldArea: 120000,
      unsoldArea: 100000,
      soldUnits: 120,
      unsoldUnits: 60,
      totalEstimatedRevenue: 1********,
      valueOfAreaUnsold: ********,
      valueOfAreaSold: ********,
      receiptsFromSoldArea: ********,
      receivablesFromSoldArea: ********,
      totalCost: ********,
      costIncurred: ********,
      costToBeIncurred: ********,
      promoterSkinInfusion: ********,
      profitInCrores: 2.5,
      profitPercentOnRevenue: 22.7,
      surplusInCrores: 3.0,
      rateSold: 12500,
      rateUnsold: 13500,
      percentCompletion: 65,
      percentCollections: 58,
      existingLoan: 'Yes',
      bankName: 'HDFC Bank',
      sanctionDate: '15-Jan-2022',
      rateOfInterest: 9.5,
      amountSanctioned: ********,
      amountDisbursed: ********,
      currentOutstandingAmount: ********,
      moratPeriod: '12 months',
      repaymentStartingDate: '15-Jan-2023',
      projectStartDate: '01-Jan-2022',
      projectLaunchDate: '15-Mar-2022',
      currentPhysicalStage: 'Slab work in progress for 10th floor (As on 30/06/2023)',
      projectCompletionDate: '31-Dec-2024',
      profitSharing: '60:40',
      developmentType: 'Owned',
      location: 'Andheri West, Mumbai',
      projectType: 'Residential',
      surveyNumbers: 'CTS No. 456/78/A',
      totalUnits: 180,
      constructionArea: '220,000 sq.ft',
      constructionCost: ********,
      salesValue: 1********,
      startDate: '10-Mar-2022',
      expectedEndDate: '15-Dec-2024',
      reraNumber: 'MH87654321',
      approvalStatus: 'Approved',
      constructionStatus: '65% Complete',
      promoterSkin: ********,
      profit: ********,
      profitPercentage: 22.7,
      surplus: ********,
      rates: '12,500/sq.ft',
      completionPercentage: 65,
      collectionPercentage: 58,
      remarks: 'Construction in progress as per schedule'
    },
    {
      id: 2,
      projectName: 'Green Valley Phase 2',
      entityName: 'ABC Developers Pvt Ltd',
      promotersNames: 'Rajesh Kumar, Priya Patel',
      projectStructure: 'G+20 Tower with 3 Basement',
      constructionApprovals: 'CC up to 10th Floor',
      latestCCDate: '20-Aug-2021',
      projectCategory: 'SOC REDEV',
      ownershipType: 'JDA',
      plotArea: '8,500 sq.mt',
      category: 'Residential',
      sharingDetails: '70:30',
      area1: 200000,
      units1: 180,
      area2: 80000,
      units2: 50,
      area3: 20000,
      units3: 10,
      soldArea: 180000,
      unsoldArea: 120000,
      soldUnits: 180,
      unsoldUnits: 60,
      totalEstimatedRevenue: ********0,
      valueOfAreaUnsold: ********,
      valueOfAreaSold: ********0,
      receiptsFromSoldArea: ********,
      receivablesFromSoldArea: ********,
      totalCost: ********,
      costIncurred: ********,
      costToBeIncurred: ********,
      promoterSkinInfusion: ********,
      profitInCrores: 3.5,
      profitPercentOnRevenue: 23.3,
      surplusInCrores: 4.5,
      rateSold: 14000,
      rateUnsold: 15000,
      percentCompletion: 40,
      percentCollections: 45,
      existingLoan: 'Yes',
      bankName: 'ICICI Bank',
      sanctionDate: '10-Apr-2021',
      rateOfInterest: 10.2,
      amountSanctioned: ********,
      amountDisbursed: ********,
      currentOutstandingAmount: ********,
      moratPeriod: '18 months',
      repaymentStartingDate: '10-Oct-2022',
      projectStartDate: '15-Mar-2021',
      projectLaunchDate: '01-Jun-2021',
      currentPhysicalStage: 'Structure completed, internal work in progress (As on 30/06/2023)',
      projectCompletionDate: '30-Aug-2025',
      profitSharing: '70:30',
      developmentType: 'JDA',
      location: 'Powai, Mumbai',
      projectType: 'Residential',
      surveyNumbers: 'S.No. 123/4, H.No. 5/6',
      totalUnits: 240,
      constructionArea: '300,000 sq.ft',
      constructionCost: ********,
      salesValue: ********0,
      startDate: '05-May-2021',
      expectedEndDate: '20-Aug-2025',
      reraNumber: 'MH45678901',
      approvalStatus: 'Approved',
      constructionStatus: '40% Complete',
      promoterSkin: ********,
      profit: ********,
      profitPercentage: 23.3,
      surplus: ********,
      rates: '14,000/sq.ft',
      completionPercentage: 40,
      collectionPercentage: 45,
      remarks: 'Premium project with high-end amenities'
    },
    {
      id: 3,
      projectName: 'Tech Hub Tower B',
      entityName: 'ABC Commercial Properties Pvt Ltd',
      promotersNames: 'Rajesh Kumar, Neha Singh',
      projectStructure: 'G+12 Office Building with 2 Basement',
      constructionApprovals: 'CC up to 6th Floor',
      latestCCDate: '10-Mar-2023',
      projectCategory: 'NORMAL LAYOUT',
      ownershipType: 'JV',
      plotArea: '4,200 sq.mt',
      category: 'Commercial',
      sharingDetails: '50:50',
      area1: 120000,
      units1: 60,
      area2: 60000,
      units2: 15,
      area3: 20000,
      units3: 5,
      soldArea: 80000,
      unsoldArea: 120000,
      soldUnits: 40,
      unsoldUnits: 40,
      totalEstimatedRevenue: *********,
      valueOfAreaUnsold: 1********,
      valueOfAreaSold: ********,
      receiptsFromSoldArea: ********,
      receivablesFromSoldArea: ********,
      totalCost: ********,
      costIncurred: ********,
      costToBeIncurred: ********,
      promoterSkinInfusion: ********,
      profitInCrores: 4.5,
      profitPercentOnRevenue: 25,
      surplusInCrores: 6.0,
      rateSold: 22000,
      rateUnsold: 24000,
      percentCompletion: 25,
      percentCollections: 30,
      existingLoan: 'Yes',
      bankName: 'Axis Bank',
      sanctionDate: '05-Feb-2023',
      rateOfInterest: 11.0,
      amountSanctioned: ********,
      amountDisbursed: ********,
      currentOutstandingAmount: ********,
      moratPeriod: '24 months',
      repaymentStartingDate: '05-Feb-2025',
      projectStartDate: '01-Dec-2022',
      projectLaunchDate: '15-Feb-2023',
      currentPhysicalStage: 'Foundation work completed, ground floor slab in progress (As on 30/06/2023)',
      projectCompletionDate: '30-Jun-2026',
      profitSharing: '50:50',
      developmentType: 'Owned',
      location: 'Bandra Kurla Complex, Mumbai',
      projectType: 'Commercial',
      surveyNumbers: 'CTS No. 789/10/B',
      totalUnits: 80,
      constructionArea: '200,000 sq.ft',
      constructionCost: ********,
      salesValue: *********,
      startDate: '15-Jan-2023',
      expectedEndDate: '30-Jun-2026',
      reraNumber: 'MH12345678',
      approvalStatus: 'Approved',
      constructionStatus: '25% Complete',
      promoterSkin: ********,
      profit: ********,
      profitPercentage: 25,
      surplus: ********,
      rates: '22,000/sq.ft',
      completionPercentage: 25,
      collectionPercentage: 30,
      remarks: 'Grade A office space with modern facilities'
    }
  ];

  // Completed Projects data for ngx-datatable with dummy data
  completedProjectRows: CompletedProjectRow[] = [
    {
      id: 1,
      projectName: 'Sunrise Heights',
      entityName: 'ABC Developers Pvt Ltd',
      promotersNames: 'Rajesh Kumar, Amit Shah',
      profitSharing: '60:40',
      promotersRole: 'Managing Director, Director',
      developmentType: 'Owned',
      location: 'Andheri East, Mumbai',
      projectType: 'Residential',
      projectStructure: 'G+14 Tower',
      surveyNumbers: 'CTS No. 123/45',
      totalUnits: 120,
      constructionArea: '150,000 sq.ft',
      constructionCost: ********,
      salesValue: ********,
      unsoldUnits: 0,
      startDate: '15-Jan-2018',
      endDate: '30-Jun-2021',
      occupancyReceived: 'Yes',
      reraNumber: 'MH12345678',
      remarks: 'Completed on time and fully sold'
    },
    {
      id: 2,
      projectName: 'Green Valley',
      entityName: 'ABC Developers Pvt Ltd',
      promotersNames: 'Rajesh Kumar, Priya Patel',
      profitSharing: '70:30',
      promotersRole: 'Managing Director, Director',
      developmentType: 'JDA',
      location: 'Powai, Mumbai',
      projectType: 'Residential',
      projectStructure: 'G+20 Tower',
      surveyNumbers: 'CTS No. 456/78',
      totalUnits: 180,
      constructionArea: '220,000 sq.ft',
      constructionCost: ********,
      salesValue: 1********,
      unsoldUnits: 0,
      startDate: '10-Mar-2019',
      endDate: '15-Dec-2022',
      occupancyReceived: 'Yes',
      reraNumber: 'MH87654321',
      remarks: 'Premium project with high-end amenities'
    },
    {
      id: 3,
      projectName: 'Tech Hub',
      entityName: 'ABC Commercial Properties Pvt Ltd',
      promotersNames: 'Rajesh Kumar, Neha Singh',
      profitSharing: '50:50',
      promotersRole: 'Managing Director, Director',
      developmentType: 'Owned',
      location: 'Bandra Kurla Complex, Mumbai',
      projectType: 'Commercial',
      projectStructure: 'G+12 Office Building',
      surveyNumbers: 'CTS No. 789/10',
      totalUnits: 60,
      constructionArea: '180,000 sq.ft',
      constructionCost: ********,
      salesValue: ********0,
      unsoldUnits: 5,
      startDate: '05-May-2017',
      endDate: '20-Aug-2020',
      occupancyReceived: 'Yes',
      reraNumber: 'MH45678901',
      remarks: 'Grade A office space with modern facilities'
    }
  ];

  // Unsold Stock & Leased Prop data for ngx-datatable with dummy data
  unsoldStockRows: UnsoldStockRow[] = [
    {
      id: 1,
      projectName: 'Tech Hub',
      location: 'Bandra Kurla Complex, Mumbai',
      category: 'Commercial',
      saleableArea: 1800,
      rate: 15000,
      value: 27000000,
      debt: ********,
      remarks: 'Premium office space with sea view'
    },
    {
      id: 2,
      projectName: 'Sunrise Heights',
      location: 'Andheri East, Mumbai',
      category: 'Residential',
      saleableArea: 1300,
      rate: 14000,
      value: 18200000,
      debt: 12000000,
      remarks: '3 BHK apartment with modern amenities'
    },
    {
      id: 3,
      projectName: 'Green Valley',
      location: 'Powai, Mumbai',
      category: 'Residential',
      saleableArea: 1500,
      rate: 16000,
      value: ********,
      debt: ********,
      remarks: 'Premium 3 BHK apartment with lake view'
    },
    {
      id: 4,
      projectName: 'City Center Mall',
      location: 'Malad West, Mumbai',
      category: 'Retail',
      saleableArea: 1100,
      rate: 18000,
      value: 19800000,
      debt: ********,
      remarks: 'Prime retail space with high footfall'
    },
    {
      id: 5,
      projectName: 'Industrial Park',
      location: 'Thane, Mumbai',
      category: 'Industrial',
      saleableArea: 5000,
      rate: 8000,
      value: ********,
      debt: ********,
      remarks: 'Warehouse space with good connectivity'
    }
  ];

  // Leased Properties data for ngx-datatable with dummy data
  leasedPropertyRows: LeasedPropertyRow[] = [
    {
      id: 1,
      projectName: 'Tech Hub Tower A',
      location: 'Bandra Kurla Complex, Mumbai',
      areaLeased: 5000,
      leaseRentPA: 6000000,
      securityDeposit: 12000000,
      debtOS: ********,
      marketValue: ********,
      remarks: 'Leased to XYZ Technologies for 9 years'
    },
    {
      id: 2,
      projectName: 'Millennium Business Park',
      location: 'Navi Mumbai',
      areaLeased: 8000,
      leaseRentPA: 9600000,
      securityDeposit: 19200000,
      debtOS: ********,
      marketValue: 1********,
      remarks: 'Leased to ABC Consulting for 5 years'
    },
    {
      id: 3,
      projectName: 'City Center Mall',
      location: 'Malad West, Mumbai',
      areaLeased: 3500,
      leaseRentPA: 5040000,
      securityDeposit: 10080000,
      debtOS: ********,
      marketValue: 63000000,
      remarks: 'Multiple retail tenants with staggered lease terms'
    },
    {
      id: 4,
      projectName: 'Sunrise Commercial Complex',
      location: 'Andheri East, Mumbai',
      areaLeased: 2800,
      leaseRentPA: 4032000,
      securityDeposit: 8064000,
      debtOS: ********,
      marketValue: ********,
      remarks: 'Leased to multiple IT companies'
    },
    {
      id: 5,
      projectName: 'Industrial Warehouse',
      location: 'Bhiwandi, Thane',
      areaLeased: 12000,
      leaseRentPA: 7200000,
      securityDeposit: ********,
      debtOS: ********,
      marketValue: ********,
      remarks: 'Leased to logistics company for 7 years'
    }
  ];

  // Land Bank & Upcoming Projects data for ngx-datatable with dummy data
  landBankRows: LandBankRow[] = [
    {
      id: 1,
      projectName: 'Green Valley Township',
      entityName: 'ABC Developers Pvt Ltd',
      ownershipType: 'Owned',
      documentationStatus: 'All approvals in process',
      location: 'Panvel, Navi Mumbai',
      projectType: 'Residential',
      expectedStartDate: '2023-12-01',
      residentialArea: 250000,
      residentialUnits: 1200,
      commercialArea: 0,
      commercialUnits: 0,
      mixedArea: 0,
      mixedUnits: 0,
      remarks: 'Land fully paid'
    },
    {
      id: 2,
      projectName: 'Tech Park Plaza',
      entityName: 'ABC Commercial Properties Pvt Ltd',
      ownershipType: 'JDA',
      documentationStatus: 'Land Acquisition',
      location: 'Hinjewadi, Pune',
      projectType: 'Commercial',
      expectedStartDate: '2024-03-01',
      residentialArea: 0,
      residentialUnits: 0,
      commercialArea: 150000,
      commercialUnits: 8,
      mixedArea: 0,
      mixedUnits: 0,
      remarks: 'JDA signed with landowner, 70% payment made'
    },
    {
      id: 3,
      projectName: 'Riverside Residences',
      entityName: 'ABC Developers Pvt Ltd',
      ownershipType: 'Owned',
      documentationStatus: 'Approval Stage',
      location: 'Kalyan, Thane',
      projectType: 'Residential',
      expectedStartDate: '2023-10-15',
      residentialArea: 120000,
      residentialUnits: 650,
      commercialArea: 0,
      commercialUnits: 0,
      mixedArea: 0,
      mixedUnits: 0,
      remarks: 'IOD received, CC awaited'
    },
    {
      id: 4,
      projectName: 'Harmony Mall',
      entityName: 'ABC Commercial Properties Pvt Ltd',
      ownershipType: 'Leased',
      documentationStatus: 'Design Stage',
      location: 'Kharghar, Navi Mumbai',
      projectType: 'Commercial',
      expectedStartDate: '2024-01-15',
      residentialArea: 0,
      residentialUnits: 0,
      commercialArea: 80000,
      commercialUnits: 120,
      mixedArea: 0,
      mixedUnits: 0,
      remarks: '99-year lease signed, design consultant appointed'
    },
    {
      id: 5,
      projectName: 'City Center Complex',
      entityName: 'ABC Infra Solutions LLP',
      ownershipType: 'JV',
      documentationStatus: 'Planning',
      location: 'Vashi, Navi Mumbai',
      projectType: 'Mixed',
      expectedStartDate: '2024-05-01',
      residentialArea: 150000,
      residentialUnits: 300,
      commercialArea: 100000,
      commercialUnits: 50,
      mixedArea: 50000,
      mixedUnits: 25,
      remarks: 'Joint venture with XYZ Builders, master planning in progress'
    }
  ];

  // Land Bank Details data for ngx-datatable with dummy data
  landBankDetailRows: LandBankDetailRow[] = [
    {
      id: 1,
      companyName: 'ABC Developers Pvt Ltd',
      location: 'Panvel, Navi Mumbai',
      landType: 'Non-Agricultural',
      ownershipType: 'Owned',
      plotArea: 25,
      plotAreaUnit: 'Acres',
      acquisitionYear: '2018',
      purchaseValue: ********0,
      marketValue: 2********,
      hasLoan: 'No',
      approvalStatus: 'All approvals in process',
      expectedLaunchDate: '2023-12-01',
      remarks: 'Prime location near upcoming airport'
    },
    {
      id: 2,
      companyName: 'ABC Commercial Properties Pvt Ltd',
      location: 'Hinjewadi, Pune',
      landType: 'Non-Agricultural',
      ownershipType: 'JDA',
      plotArea: 15,
      plotAreaUnit: 'Acres',
      acquisitionYear: '2020',
      purchaseValue: ********0,
      marketValue: ********0,
      hasLoan: 'Yes',
      approvalStatus: 'Land Acquisition',
      expectedLaunchDate: '2024-03-01',
      remarks: 'JDA signed with landowner, 70% payment made'
    },
    {
      id: 3,
      companyName: 'Rajesh Kumar (Individual)',
      location: 'Lonavala, Pune',
      landType: 'Agricultural',
      ownershipType: 'Owned',
      plotArea: 10,
      plotAreaUnit: 'Acres',
      acquisitionYear: '2015',
      purchaseValue: ********,
      marketValue: 1********,
      hasLoan: 'No',
      approvalStatus: 'NA Conversion Pending',
      expectedLaunchDate: '2024-06-15',
      remarks: 'Hill station property with scenic views'
    },
    {
      id: 4,
      companyName: 'ABC Infra Solutions LLP',
      location: 'Kalyan, Thane',
      landType: 'Non-Agricultural',
      ownershipType: 'Owned',
      plotArea: 12,
      plotAreaUnit: 'Acres',
      acquisitionYear: '2019',
      purchaseValue: 1********,
      marketValue: *********,
      hasLoan: 'Yes',
      approvalStatus: 'Approval Stage',
      expectedLaunchDate: '2023-10-15',
      remarks: 'IOD received, CC awaited'
    },
    {
      id: 5,
      companyName: 'Mehta & Sons (Partnership Firm)',
      location: 'Kharghar, Navi Mumbai',
      landType: 'Non-Agricultural',
      ownershipType: 'JDA',
      plotArea: 8,
      plotAreaUnit: 'Acres',
      acquisitionYear: '2021',
      purchaseValue: *********,
      marketValue: *********,
      hasLoan: 'No',
      approvalStatus: 'Design Stage',
      expectedLaunchDate: '2024-01-15',
      remarks: 'Premium location near central business district'
    }
  ];

  // Project Land Details data for ngx-datatable with dummy data
  projectLandDetailRows: ProjectLandDetailRow[] = [
    {
      id: 1,
      agreementType: 'Sale Deed',
      documentDate: '2022-05-15',
      documentNo: 'SD/2022/1234',
      surveyNo: '123/4A',
      areaSqMtrs: 25000,
      areaGunthaAcre: 6.18,
      areaUnit: 'Acre',
      partiesNames: 'ABC Developers Pvt Ltd and XYZ Landowners',
      considerationDetails: 'Rs. 15 Crores for 6.18 Acres of land'
    },
    {
      id: 2,
      agreementType: 'Joint Development Agreement',
      documentDate: '2021-08-22',
      documentNo: 'JDA/2021/5678',
      surveyNo: '456/7B',
      areaSqMtrs: 15000,
      areaGunthaAcre: 3.71,
      areaUnit: 'Acre',
      partiesNames: 'ABC Commercial Properties Pvt Ltd and PQR Landowners',
      considerationDetails: '60% revenue share to landowner, 40% to developer'
    },
    {
      id: 3,
      agreementType: 'Development Agreement',
      documentDate: '2020-11-10',
      documentNo: 'DA/2020/9012',
      surveyNo: '789/2C',
      areaSqMtrs: 40000,
      areaGunthaAcre: 9.88,
      areaUnit: 'Acre',
      partiesNames: 'ABC Developers Pvt Ltd and Rajesh Kumar',
      considerationDetails: 'Rs. 5 Crores + 30% constructed area to landowner'
    },
    {
      id: 4,
      agreementType: 'Sale Deed',
      documentDate: '2019-03-25',
      documentNo: 'SD/2019/3456',
      surveyNo: '321/5D',
      areaSqMtrs: 12000,
      areaGunthaAcre: 2.97,
      areaUnit: 'Acre',
      partiesNames: 'ABC Infra Solutions LLP and MNO Landowners',
      considerationDetails: 'Rs. 12 Crores for 2.97 Acres of land'
    },
    {
      id: 5,
      agreementType: 'Joint Venture Agreement',
      documentDate: '2021-06-18',
      documentNo: 'JV/2021/7890',
      surveyNo: '654/8E',
      areaSqMtrs: 8000,
      areaGunthaAcre: 1.98,
      areaUnit: 'Acre',
      partiesNames: 'ABC Developers Pvt Ltd and Mehta & Sons',
      considerationDetails: '50:50 profit sharing between both parties'
    }
  ];

  // Current date for cost incurred calculation
  currentDate: string = new Date().toLocaleDateString('en-GB', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  }).replace(/\//g, '/');

  // Compiled Cost data for ngx-datatable with dummy data
  compiledCostRows: CompiledCostRow[] = [
    {
      id: 1,
      particulars: 'Land Cost',
      totalCost: ********,
      costIncurred: ********,
      costToBeIncurred: 0,
      percentage: 25.0
    },
    {
      id: 2,
      particulars: 'Construction Cost',
      totalCost: ********0,
      costIncurred: ********,
      costToBeIncurred: ********,
      percentage: 50.0
    },
    {
      id: 3,
      particulars: 'Approval Cost',
      totalCost: ********,
      costIncurred: 12000000,
      costToBeIncurred: 3000000,
      percentage: 7.5
    },
    {
      id: 4,
      particulars: 'Professional Fees',
      totalCost: ********,
      costIncurred: 5000000,
      costToBeIncurred: 5000000,
      percentage: 5.0
    },
    {
      id: 5,
      particulars: 'Marketing Cost',
      totalCost: 8000000,
      costIncurred: 3000000,
      costToBeIncurred: 5000000,
      percentage: 4.0
    },
    {
      id: 6,
      particulars: 'Contingency',
      totalCost: 7000000,
      costIncurred: 2000000,
      costToBeIncurred: 5000000,
      percentage: 3.5
    },
    {
      id: 7,
      particulars: 'Finance Cost',
      totalCost: ********,
      costIncurred: 4000000,
      costToBeIncurred: 6000000,
      percentage: 5.0
    }
  ];

  // Fund Infusion data for ngx-datatable with dummy data
  fundInfusionRows: FundInfusionRow[] = [
    {
      id: 1,
      particulars: 'Bookings / Advances',
      total: ********,
      infusion: ********,
      infusionTillDate: ********,
      toBeInfused: ********,
      percentage: 30.0
    },
    {
      id: 2,
      particulars: 'Project Finance',
      total: ********0,
      infusion: ********,
      infusionTillDate: ********,
      toBeInfused: ********,
      percentage: 50.0
    },
    {
      id: 3,
      particulars: 'Promoter\'s Contribution',
      total: ********,
      infusion: ********,
      infusionTillDate: ********,
      toBeInfused: ********,
      percentage: 15.0
    },
    {
      id: 4,
      particulars: 'Unsecured Loans',
      total: ********,
      infusion: 5000000,
      infusionTillDate: 3000000,
      toBeInfused: 7000000,
      percentage: 5.0
    }
  ];

  // Debt Details data for ngx-datatable with dummy data
  debtDetailsRows: DebtDetailsRow[] = [
    {
      id: 1,
      borrowerName: 'ABC Developers Pvt Ltd',
      entityType: 'ENTITY',
      nameOfLender: 'HDFC Bank',
      typeOfFacility: 'TERM LOAN',
      loanAccountNo: 'HDFC123456789',
      individualGuarantee: 'YES',
      dateOfSanction: '15-Jan-2023',
      dateOfDisbursement: '01-Feb-2023',
      sanctionedAmount: ********,
      disbursedAmount: ********,
      utilized: ********,
      roa: 10.5,
      repaid: 5000000,
      emiAmount: 550000,
      repaymentBankAccountNo: 'HDFC987654321',
      totalEmi: 60,
      emiPaid: 10,
      originFees: 500000,
      currentOutstanding: ********,
      loanTenor: '5 Years',
      moratorium: '6 Months',
      detailsOfSecurityCreated: 'Property Mortgage',
      overdueAmount: 0,
      detailsOfDefault: 'N/A',
      remarksForDelay: 'N/A'
    },
    {
      id: 2,
      borrowerName: 'XYZ Constructions Ltd',
      entityType: 'ENTITY',
      nameOfLender: 'ICICI Bank',
      typeOfFacility: 'CASH CREDIT',
      loanAccountNo: 'ICICI987654321',
      individualGuarantee: 'NO',
      dateOfSanction: '10-Mar-2023',
      dateOfDisbursement: '25-Mar-2023',
      sanctionedAmount: ********,
      disbursedAmount: ********,
      utilized: ********,
      roa: 11.25,
      repaid: 2000000,
      emiAmount: 350000,
      repaymentBankAccountNo: 'ICICI123456789',
      totalEmi: 48,
      emiPaid: 6,
      originFees: 300000,
      currentOutstanding: ********,
      loanTenor: '4 Years',
      moratorium: '3 Months',
      detailsOfSecurityCreated: 'Property Mortgage + Personal Guarantee',
      overdueAmount: 350000,
      detailsOfDefault: 'EMI for Apr 2023',
      remarksForDelay: 'Payment processing delay'
    },
    {
      id: 3,
      borrowerName: 'PQR Realtors',
      entityType: 'INDIVIDUAL',
      nameOfLender: 'SBI Bank',
      typeOfFacility: 'TERM LOAN',
      loanAccountNo: 'SBI567891234',
      individualGuarantee: 'YES',
      dateOfSanction: '05-Apr-2023',
      dateOfDisbursement: '20-Apr-2023',
      sanctionedAmount: ********,
      disbursedAmount: ********,
      utilized: ********,
      roa: 9.75,
      repaid: 1500000,
      emiAmount: 250000,
      repaymentBankAccountNo: 'SBI432156789',
      totalEmi: 72,
      emiPaid: 3,
      originFees: 200000,
      currentOutstanding: ********,
      loanTenor: '6 Years',
      moratorium: 'None',
      detailsOfSecurityCreated: 'Property Mortgage',
      overdueAmount: 0,
      detailsOfDefault: 'N/A',
      remarksForDelay: 'N/A'
    }
  ];

  constructor(
    private modalService: NgbModal,
    private router: Router,
    private route: ActivatedRoute,
    private pipe: DecimalPipe
  ) { }

  ngOnInit(): void {
    // Initialize component
    // No pre-selected status values

    // Initialize list view data
    this.filteredOpsTeam = [...this.opsTeamData];

    // Initialize filtered documents
    this.filteredDocuments = [...this.documents];

    // Initialize shareholders
    this.initializeShareholders();

    // Initialize the showReceivedDateColumn flag
    this.updateShowReceivedDateColumn();

    // Add explainDate and receivedDate properties to all documents if they don't exist
    this.documents.forEach(doc => {
      if (!doc.hasOwnProperty('explainDate')) {
        doc.explainDate = '';
      }
      if (!doc.hasOwnProperty('receivedDate')) {
        doc.receivedDate = '';
      }
    });

    // Initialize section follow-up fields
    this.sectionFollowUpDate = this.getCurrentDate();
    this.sectionFollowUpTime = this.getCurrentTime();
    this.sectionFollowUpNotes = '';

    // Initialize email compose data
    this.peoples = PeoplesData.peoples;
    this.selectedTo = [this.peoples[2].id];
    this.selectedCc = [this.peoples[3].id, this.peoples[4].id, this.peoples[5].id];

    // Set up search filter
    this.searchTerm.valueChanges.subscribe(term => {
      this.filteredOpsTeam = search(term, this.pipe);
      this.collectionSize = this.filteredOpsTeam.length;
      this.page = 1;
    });

    // Check route data for showing details form
    this.route.data.subscribe(data => {
      if (data['showDetailsForm']) {
        this.showListView = false;
        this.showDetailsForm = true;

        // Reset to first tab
        this.activeTabId = 1;
      }
    });

    // Check for route parameters (for edit mode)
    this.route.params.subscribe(params => {
      if (params['id']) {
        const id = +params['id']; // Convert to number
        this.selectedItemId = id;
        this.showListView = false;
        this.showDetailsForm = true;
      }
    });

    // Check for query parameters and fragment in URL
    this.router.events.subscribe(event => {
      if (event instanceof NavigationEnd) {
        // Get query parameters
        const queryParams = this.router.parseUrl(this.router.url).queryParams;

        // Check if we need to activate a specific tab
        if (queryParams['activateTab'] === 'cam-note') {
          // Set active tab to CAM Note (3)
          this.activeTabId = 3;

          // Check if we need to activate a specific nested tab
          if (queryParams['activateNestedTab'] === 'debt-details') {
            // Set active nested tab to Debt Details (18)
            this.activeCamNoteTabId = 18;
          } else if (queryParams['activateNestedTab'] === 'sales-plan') {
            // Set active nested tab to Sales Plan (16)
            this.activeCamNoteTabId = 16;
          }
        }

        // Get fragment for scrolling
        const fragment = this.router.parseUrl(this.router.url).fragment;
        if (fragment) {
          setTimeout(() => {
            const element = document.getElementById(fragment);
            if (element) {
              element.scrollIntoView({ behavior: 'smooth' });

              // If it's the sales-plan fragment and no specific tab is requested,
              // activate the Sales Plan tab
              if (fragment === 'sales-plan' && !queryParams['activateTab']) {
                // Set active tab to Sales Plan (16)
                this.activeTabId = 16;
              } else if (fragment === 'debt-details' && !queryParams['activateTab']) {
                // Set active tab to Debt Details (18)
                this.activeTabId = 18;
              }
            }
          }, 300); // Increased timeout to ensure DOM is fully loaded
        }
      }
    });
  }

  // Update status when dropdown changes
  onStatusChange(docId: number, event: Event): void {
    const selectElement = event.target as HTMLSelectElement;
    this.documentStatus[docId] = selectElement.value;

    // Update the document in the table
    const docIndex = this.documents.findIndex(doc => doc.id === docId);
    if (docIndex !== -1) {
      this.documents[docIndex].status = selectElement.value;

      // If status is "received", set the received date to today's date
      if (selectElement.value === 'received') {
        this.documents[docIndex].receivedDate = new Date().toISOString().split('T')[0]; // Today's date in YYYY-MM-DD format
      }

      // No need to show follow-up form anymore
      this.followUpFormVisible[docId] = false;

      // Create a new array reference to trigger change detection
      this.documents = [...this.documents];

      // Update the showReceivedDateColumn flag
      this.updateShowReceivedDateColumn();
    }
  }

  // Check if follow-up form should be shown for a document
  shouldShowFollowUpForm(docId: number): boolean {
    return this.followUpFormVisible[docId] === true;
  }

  // Toggle follow-up form visibility
  toggleFollowUpForm(docId: number): void {
    this.followUpFormVisible[docId] = !this.followUpFormVisible[docId];
  }

  // Check if document has pending status
  hasDocumentPendingStatus(docId: number): boolean {
    const docIndex = this.documents.findIndex(doc => doc.id === docId);
    return docIndex !== -1 && this.documents[docIndex].status === 'pending';
  }

  // Check if section has any documents with pending status
  hasSectionPendingDocuments(sectionId: number): boolean {
    const sectionDocs = this.getDocumentsBySection(sectionId);
    return sectionDocs.some(doc => doc.status === 'pending');
  }

  // Check if any document has "received" status and update the flag
  updateShowReceivedDateColumn(): void {
    this.showReceivedDateColumn = this.documents.some(doc => doc.status === 'received');
  }

  // Update document date
  updateDate(docId: number, event: Event): void {
    const inputElement = event.target as HTMLInputElement;
    const docIndex = this.documents.findIndex(doc => doc.id === docId);
    if (docIndex !== -1) {
      this.documents[docIndex].date = inputElement.value;
      // Create a new array reference to trigger change detection
      this.documents = [...this.documents];
    }
  }

  // Update document time
  updateTime(docId: number, event: Event): void {
    const inputElement = event.target as HTMLInputElement;
    const docIndex = this.documents.findIndex(doc => doc.id === docId);
    if (docIndex !== -1) {
      this.documents[docIndex].time = inputElement.value;
      // Create a new array reference to trigger change detection
      this.documents = [...this.documents];
    }
  }

  // Update document notes
  updateNotes(docId: number, event: Event): void {
    const inputElement = event.target as HTMLInputElement;
    const docIndex = this.documents.findIndex(doc => doc.id === docId);
    if (docIndex !== -1) {
      this.documents[docIndex].notes = inputElement.value;
      // Create a new array reference to trigger change detection
      this.documents = [...this.documents];
    }
  }

  // Update explain date
  updateExplainDate(docId: number, event: Event): void {
    const inputElement = event.target as HTMLInputElement;
    const docIndex = this.documents.findIndex(doc => doc.id === docId);
    if (docIndex !== -1) {
      this.documents[docIndex].explainDate = inputElement.value;
      // Create a new array reference to trigger change detection
      this.documents = [...this.documents];
    }
  }

  // Update received date
  updateReceivedDate(docId: number, event: Event): void {
    const inputElement = event.target as HTMLInputElement;
    const docIndex = this.documents.findIndex(doc => doc.id === docId);
    if (docIndex !== -1) {
      this.documents[docIndex].receivedDate = inputElement.value;
      // Create a new array reference to trigger change detection
      this.documents = [...this.documents];
    }
  }

  // Add follow-up data when Add button is clicked
  addFollowUp(docId: number): void {
    if (this.documentStatus[docId] === 'pending' || this.hasDocumentPendingStatus(docId)) {
      const docIndex = this.documents.findIndex(doc => doc.id === docId);
      if (docIndex !== -1) {
        const doc = this.documents[docIndex];

        // Initialize the array if it doesn't exist
        if (!this.followUpData[docId]) {
          this.followUpData[docId] = [];
        }

        // Add new follow-up to the array
        this.followUpData[docId].push({
          date: doc.date,
          time: doc.time,
          notes: doc.notes,
          timestamp: new Date().getTime() // Add timestamp for sorting
        });

        // Reset the form fields
        this.documents[docIndex].date = new Date().toISOString().split('T')[0]; // Today's date
        this.documents[docIndex].time = new Date().toTimeString().slice(0, 5); // Current time
        this.documents[docIndex].notes = '';

        // Hide the follow-up form after adding a follow-up
        this.followUpFormVisible[docId] = false;

        // Create a new array reference to trigger change detection
        this.documents = [...this.documents];
      }
    }
  }

  // Property to store all follow-ups for a section
  allSectionFollowUps: Array<{ documentName: string, date: string, time: string, notes: string, timestamp: number }> = [];

  // Reference to the follow-up history modal
  @ViewChild('followUpHistoryModal') followUpHistoryModal: any;

  // Get the latest follow-up for a document
  getLatestFollowUp(docId: number | string): { date: string, time: string, notes: string, timestamp: number } | null {
    // If it's a document ID (number)
    if (typeof docId === 'number') {
      if (!this.followUpData[docId] || this.followUpData[docId].length === 0) {
        return null;
      }

      // Sort by timestamp (descending) and return the first item
      return [...this.followUpData[docId]].sort((a, b) => b.timestamp - a.timestamp)[0];
    }
    // If it's a section ID (string)
    else if (typeof docId === 'string') {
      // Get all documents in this section
      const sectionDocuments = this.getDocumentsBySection(Number(docId));

      // Collect all follow-ups for documents in this section
      let allSectionFollowUps: Array<{ date: string, time: string, notes: string, timestamp: number }> = [];

      sectionDocuments.forEach(doc => {
        if (this.followUpData[doc.id] && this.followUpData[doc.id].length > 0) {
          allSectionFollowUps = [...allSectionFollowUps, ...this.followUpData[doc.id]];
        }
      });

      if (allSectionFollowUps.length === 0) {
        return null;
      }

      // Sort by timestamp (descending) and return the most recent one
      return allSectionFollowUps.sort((a, b) => b.timestamp - a.timestamp)[0];
    }

    return null;
  }

  // Get the latest N follow-ups for a document
  getLatestFollowUps(docId: number, count: number): Array<{ date: string, time: string, notes: string, timestamp: number }> {
    if (!this.followUpData[docId] || this.followUpData[docId].length === 0) {
      return [];
    }

    // Sort by timestamp (descending) and return the first N items
    return [...this.followUpData[docId]]
      .sort((a, b) => b.timestamp - a.timestamp)
      .slice(0, count);
  }

  // Get row height based on content
  getRowHeight(row: any): number {
    if (!row) return 180;

    // Base height for the Add Follow-up section
    let height = 180;

    // If there are follow-ups, add height for the follow-up history section
    if (this.hasFollowUp(row.id)) {
      // Calculate based on number of follow-ups (each row is about 50px)
      const followUpCount = this.getFollowUpCount(row.id);
      // Base height for the table header + some padding
      const tableHeaderHeight = 100;
      // Each row is approximately 50px
      const rowHeight = 50;
      // Calculate total height needed for the follow-up history
      const followUpHeight = tableHeaderHeight + (Math.min(followUpCount, 5) * rowHeight);

      // Add to the base height
      height += followUpHeight;
    }

    // Add some extra padding
    return height + 50;
  }

  // Open modal to show full follow-up history
  openFollowUpHistoryModal(docId: number): void {
    const document = this.documents.find(doc => doc.id === docId);
    if (!document) return;

    const modalRef = this.modalService.open(FollowUpHistoryModalComponent, {
      size: 'lg',
      centered: true,
      scrollable: true,
      backdrop: 'static'
    });

    // Pass data to the modal component
    modalRef.componentInstance.docId = docId;
    modalRef.componentInstance.documentName = document.name;
    modalRef.componentInstance.followUps = this.followUpData[docId] || [];
  }

  // Check if follow-up data exists for a document
  hasFollowUp(docId: number): boolean {
    return this.followUpData[docId] !== undefined && this.followUpData[docId].length > 0;
  }

  // Get the number of follow-ups for a document
  getFollowUpCount(docId: number): number {
    return this.followUpData[docId]?.length || 0;
  }

  // Toggle checkbox for a document
  toggleCheck(docId: number): void {
    const docIndex = this.documents.findIndex(doc => doc.id === docId);
    if (docIndex !== -1) {
      this.documents[docIndex].checked = !this.documents[docIndex].checked;
      // Create a new array reference to trigger change detection
      this.documents = [...this.documents];
    }
  }

  // Get count of selected documents
  getSelectedDocumentsCount(): number {
    return this.documents.filter(doc => doc.checked).length;
  }

  // Apply bulk status change to selected documents
  applyBulkStatus(status: 'pending' | 'received'): void {
    const selectedDocuments = this.documents.filter(doc => doc.checked);

    if (selectedDocuments.length === 0) {
      return;
    }

    // Update status for all selected documents
    selectedDocuments.forEach(doc => {
      const docIndex = this.documents.findIndex(d => d.id === doc.id);
      if (docIndex !== -1) {
        this.documents[docIndex].status = status;
        // If status is received, set received date to current date
        if (status === 'received') {
          this.documents[docIndex].receivedDate = this.getCurrentDate();
        } else {
          // Clear received date if status is pending
          this.documents[docIndex].receivedDate = '';
        }
        // Uncheck the document after applying status
        this.documents[docIndex].checked = false;
      }
    });

    // Create a new array reference to trigger change detection
    this.documents = [...this.documents];

    // Update the showReceivedDateColumn flag to show/hide the Received Date column
    this.updateShowReceivedDateColumn();

    // Show success message
    const statusText = status === 'pending' ? 'Pending' : 'Received';
    alert(`${selectedDocuments.length} document(s) marked as ${statusText}`);
  }

  // Handle checkbox change for government banks
  onGovtBankCheckboxChange(bankId: number, event: Event): void {
    event.stopPropagation(); // Prevent the dropdown from closing
    const checkbox = event.target as HTMLInputElement;

    if (checkbox.checked) {
      // Add the bank if it's not already in the array
      if (!this.selectedGovtBanks.includes(bankId)) {
        this.selectedGovtBanks.push(bankId);
      }
    } else {
      // Remove the bank
      this.selectedGovtBanks = this.selectedGovtBanks.filter(id => id !== bankId);
    }
  }

  // Handle checkbox change for private banks
  onPrivateBankCheckboxChange(bankId: number, event: Event): void {
    event.stopPropagation(); // Prevent the dropdown from closing
    const checkbox = event.target as HTMLInputElement;

    if (checkbox.checked) {
      // Add the bank if it's not already in the array
      if (!this.selectedPrivateBanks.includes(bankId)) {
        this.selectedPrivateBanks.push(bankId);
      }
    } else {
      // Remove the bank
      this.selectedPrivateBanks = this.selectedPrivateBanks.filter(id => id !== bankId);
    }
  }

  // Handle checkbox change for NBFCs
  onNBFCCheckboxChange(nbfcId: number, event: Event): void {
    event.stopPropagation(); // Prevent the dropdown from closing
    const checkbox = event.target as HTMLInputElement;

    if (checkbox.checked) {
      // Add the NBFC if it's not already in the array
      if (!this.selectedNBFCs.includes(nbfcId)) {
        this.selectedNBFCs.push(nbfcId);
      }
    } else {
      // Remove the NBFC
      this.selectedNBFCs = this.selectedNBFCs.filter(id => id !== nbfcId);
    }
  }

  // Get text to display in the government banks dropdown button
  getSelectedGovtBanksText(): string {
    if (this.selectedGovtBanks.length === 0) {
      return 'Select Government Banks';
    }

    if (this.selectedGovtBanks.length === 1) {
      const bank = this.govtBanks.find(b => b.id === this.selectedGovtBanks[0]);
      return bank ? bank.name : 'Select Government Banks';
    }

    return `${this.selectedGovtBanks.length} government banks selected`;
  }

  // Get text to display in the private banks dropdown button
  getSelectedPrivateBanksText(): string {
    if (this.selectedPrivateBanks.length === 0) {
      return 'Select Private Banks';
    }

    if (this.selectedPrivateBanks.length === 1) {
      const bank = this.privateBanks.find(b => b.id === this.selectedPrivateBanks[0]);
      return bank ? bank.name : 'Select Private Banks';
    }

    return `${this.selectedPrivateBanks.length} private banks selected`;
  }

  // Get text to display in the NBFCs dropdown button
  getSelectedNBFCsText(): string {
    if (this.selectedNBFCs.length === 0) {
      return 'Select NBFCs';
    }

    if (this.selectedNBFCs.length === 1) {
      const nbfc = this.nbfcs.find(n => n.id === this.selectedNBFCs[0]);
      return nbfc ? nbfc.name : 'Select NBFCs';
    }

    return `${this.selectedNBFCs.length} NBFCs selected`;
  }

  // Check if a government bank is selected
  isGovtBankSelected(bankId: number): boolean {
    return this.selectedGovtBanks.includes(bankId);
  }

  // Check if a private bank is selected
  isPrivateBankSelected(bankId: number): boolean {
    return this.selectedPrivateBanks.includes(bankId);
  }

  // Check if an NBFC is selected
  isNBFCSelected(nbfcId: number): boolean {
    return this.selectedNBFCs.includes(nbfcId);
  }

  // Handle checkbox change for Consultancies
  onConsultancyCheckboxChange(consultancyId: number, event: Event): void {
    event.stopPropagation(); // Prevent the dropdown from closing
    const checkbox = event.target as HTMLInputElement;

    if (checkbox.checked) {
      // Add the consultancy if it's not already in the array
      if (!this.selectedConsultancies.includes(consultancyId)) {
        this.selectedConsultancies.push(consultancyId);
      }
    } else {
      // Remove the consultancy
      this.selectedConsultancies = this.selectedConsultancies.filter(id => id !== consultancyId);
    }
  }

  // Get text to display in the consultancies dropdown button
  getSelectedConsultanciesText(): string {
    if (this.selectedConsultancies.length === 0) {
      return 'Select Corporate Consultancies';
    }

    if (this.selectedConsultancies.length === 1) {
      const consultancy = this.consultancies.find(c => c.id === this.selectedConsultancies[0]);
      return consultancy ? consultancy.name : 'Select Corporate Consultancies';
    }

    return `${this.selectedConsultancies.length} consultancies selected`;
  }

  // Check if a consultancy is selected
  isConsultancySelected(consultancyId: number): boolean {
    return this.selectedConsultancies.includes(consultancyId);
  }

  // Handle checkbox change for Fund Houses
  onFundHouseCheckboxChange(fundHouseId: number, event: Event): void {
    event.stopPropagation(); // Prevent the dropdown from closing
    const checkbox = event.target as HTMLInputElement;

    if (checkbox.checked) {
      // Add the fund house if it's not already in the array
      if (!this.selectedFundHouses.includes(fundHouseId)) {
        this.selectedFundHouses.push(fundHouseId);
      }
    } else {
      // Remove the fund house
      this.selectedFundHouses = this.selectedFundHouses.filter(id => id !== fundHouseId);
    }
  }

  // Get text to display in the fund houses dropdown button
  getSelectedFundHousesText(): string {
    if (this.selectedFundHouses.length === 0) {
      return 'Select Fund Houses';
    }

    if (this.selectedFundHouses.length === 1) {
      const fundHouse = this.fundHouses.find(f => f.id === this.selectedFundHouses[0]);
      return fundHouse ? fundHouse.name : 'Select Fund Houses';
    }

    return `${this.selectedFundHouses.length} fund houses selected`;
  }

  // Check if a fund house is selected
  isFundHouseSelected(fundHouseId: number): boolean {
    return this.selectedFundHouses.includes(fundHouseId);
  }

  // Check if any bank/institution is selected
  hasAnyBankSelected(): boolean {
    return this.selectedGovtBanks.length > 0 ||
           this.selectedPrivateBanks.length > 0 ||
           this.selectedNBFCs.length > 0 ||
           this.selectedConsultancies.length > 0 ||
           this.selectedFundHouses.length > 0;
  }

  // Get details of selected government banks
  getSelectedGovtBankDetails(): any[] {
    return this.govtBanks.filter(bank => this.selectedGovtBanks.includes(bank.id));
  }

  // Get details of selected private banks
  getSelectedPrivateBankDetails(): any[] {
    return this.privateBanks.filter(bank => this.selectedPrivateBanks.includes(bank.id));
  }

  // Get details of selected NBFCs
  getSelectedNBFCDetails(): any[] {
    return this.nbfcs.filter(nbfc => this.selectedNBFCs.includes(nbfc.id));
  }

  // Get details of selected consultancies
  getSelectedConsultancyDetails(): any[] {
    return this.consultancies.filter(consultancy => this.selectedConsultancies.includes(consultancy.id));
  }

  // Get details of selected fund houses
  getSelectedFundHouseDetails(): any[] {
    return this.fundHouses.filter(fundHouse => this.selectedFundHouses.includes(fundHouse.id));
  }

  // Remove a selected bank/institution
  removeSelectedBank(type: string, id: number): void {
    switch(type) {
      case 'govt':
        this.selectedGovtBanks = this.selectedGovtBanks.filter(bankId => bankId !== id);
        break;
      case 'private':
        this.selectedPrivateBanks = this.selectedPrivateBanks.filter(bankId => bankId !== id);
        break;
      case 'nbfc':
        this.selectedNBFCs = this.selectedNBFCs.filter(nbfcId => nbfcId !== id);
        break;
      case 'consultancy':
        this.selectedConsultancies = this.selectedConsultancies.filter(consultancyId => consultancyId !== id);
        break;
      case 'fundHouse':
        this.selectedFundHouses = this.selectedFundHouses.filter(fundHouseId => fundHouseId !== id);
        break;
    }
  }

  // Handle institute form submission
  submitInstituteForm(): void {
    this.instituteFormSubmitted = true;

    // Initialize status objects for all selected banks
    this.initializeBankStatuses();
  }

  // Reset institute form
  resetInstituteForm(): void {
    this.selectedGovtBanks = [];
    this.selectedPrivateBanks = [];
    this.selectedNBFCs = [];
    this.selectedConsultancies = [];
    this.selectedFundHouses = [];
    this.instituteFormSubmitted = false;
    this.bankStatuses = {};

    // Reset data explanation fields
    this.dataExplanation = '';
    this.dataExplanationStatus = '';
    this.dataExplanationNotes = '';
    this.dataExplanationFollowUpDate = '';
    this.dataExplanationFollowUpTime = '';
    this.dataExplanationFollowUps = [];
  }

  // Get bank status key
  getBankStatusKey(type: string, id: number): string {
    return `${type}_${id}`;
  }

  // Initialize status objects for all selected banks
  initializeBankStatuses(): void {
    // Government Banks
    this.selectedGovtBanks.forEach(bankId => {
      const key = this.getBankStatusKey('govt', bankId);
      if (!this.bankStatuses[key]) {
        this.bankStatuses[key] = {
          dataSent: '',
          dataExplained: '',
          inPrinciple: '',
          followUpDate: this.getCurrentDate(),
          followUpTime: this.getCurrentTime(),
          notes: '',
          action: ''
        };
      }
    });

    // Private Banks
    this.selectedPrivateBanks.forEach(bankId => {
      const key = this.getBankStatusKey('private', bankId);
      if (!this.bankStatuses[key]) {
        this.bankStatuses[key] = {
          dataSent: '',
          dataExplained: '',
          inPrinciple: '',
          followUpDate: this.getCurrentDate(),
          followUpTime: this.getCurrentTime(),
          notes: '',
          action: ''
        };
      }
    });

    // NBFCs
    this.selectedNBFCs.forEach(nbfcId => {
      const key = this.getBankStatusKey('nbfc', nbfcId);
      if (!this.bankStatuses[key]) {
        this.bankStatuses[key] = {
          dataSent: '',
          dataExplained: '',
          inPrinciple: '',
          followUpDate: this.getCurrentDate(),
          followUpTime: this.getCurrentTime(),
          notes: '',
          action: ''
        };
      }
    });

    // Consultancies
    this.selectedConsultancies.forEach(consultancyId => {
      const key = this.getBankStatusKey('consultancy', consultancyId);
      if (!this.bankStatuses[key]) {
        this.bankStatuses[key] = {
          dataSent: '',
          dataExplained: '',
          inPrinciple: '',
          followUpDate: this.getCurrentDate(),
          followUpTime: this.getCurrentTime(),
          notes: '',
          action: ''
        };
      }
    });

    // Fund Houses
    this.selectedFundHouses.forEach(fundHouseId => {
      const key = this.getBankStatusKey('fundHouse', fundHouseId);
      if (!this.bankStatuses[key]) {
        this.bankStatuses[key] = {
          dataSent: '',
          dataExplained: '',
          inPrinciple: '',
          followUpDate: this.getCurrentDate(),
          followUpTime: this.getCurrentTime(),
          notes: '',
          action: ''
        };
      }
    });
  }

  // Get current date in YYYY-MM-DD format
  getCurrentDate(): string {
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  // Get current time in HH:MM format
  getCurrentTime(): string {
    const now = new Date();
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    return `${hours}:${minutes}`;
  }

  // Save follow-up history for a section
  saveFollowUpHistory(sectionId: number): void {
    // Here you would typically save the follow-up history to a database
    // For now, we'll just show an alert
    alert('Follow-up history saved successfully!');

    // In a real application, you would make an API call here to save the data
    console.log('Saving follow-up history for section:', sectionId);

    // Get the latest follow-up for this section to display in the UI
    const latestFollowUp = this.getLatestFollowUp(sectionId);
    console.log('Latest follow-up:', latestFollowUp);
  }

  // Open the follow-up history modal for a section
  openSectionFollowUpHistoryModal(sectionId: number): void {
    // Get all documents in this section
    const sectionDocuments = this.getDocumentsBySection(sectionId);

    // Collect all follow-ups for documents in this section
    this.allSectionFollowUps = [];

    sectionDocuments.forEach(doc => {
      if (this.followUpData[doc.id] && this.followUpData[doc.id].length > 0) {
        // Add document name to each follow-up entry
        const followUpsWithDocName = this.followUpData[doc.id].map(followUp => ({
          ...followUp,
          documentName: doc.name
        }));

        this.allSectionFollowUps = [...this.allSectionFollowUps, ...followUpsWithDocName];
      }
    });

    // Sort by timestamp (descending)
    this.allSectionFollowUps.sort((a, b) => b.timestamp - a.timestamp);

    // Open the modal
    this.modalService.open(this.followUpHistoryModal, { size: 'lg', centered: true });
  }

  // Add follow-up for all pending documents in a section
  addSectionFollowUp(sectionId: number): void {
    // Get all documents in this section with pending status
    const pendingDocs = this.getDocumentsBySection(sectionId).filter(doc => doc.status === 'pending');

    if (pendingDocs.length > 0) {
      // Add follow-up for each pending document
      pendingDocs.forEach(doc => {
        // Initialize the array if it doesn't exist
        if (!this.followUpData[doc.id]) {
          this.followUpData[doc.id] = [];
        }

        // Add new follow-up to the array
        this.followUpData[doc.id].push({
          date: this.sectionFollowUpDate,
          time: this.sectionFollowUpTime,
          notes: this.sectionFollowUpNotes,
          timestamp: new Date().getTime() // Add timestamp for sorting
        });
      });

      // Reset the form fields
      this.sectionFollowUpDate = this.getCurrentDate();
      this.sectionFollowUpTime = this.getCurrentTime();
      this.sectionFollowUpNotes = '';

      // Hide the follow-up form
      this.followUpFormVisible[sectionId] = false;

      // Create a new array reference to trigger change detection
      this.documents = [...this.documents];
    }
  }

  // Check if data explanation follow-up form should be shown
  shouldShowDataExplanationFollowUp(): boolean {
    return this.dataExplanationStatus === 'Rejected' || this.dataExplanationStatus === 'Query';
  }

  // Handle data explanation status change
  onDataExplanationStatusChange(event: Event): void {
    const selectElement = event.target as HTMLSelectElement;
    this.dataExplanationStatus = selectElement.value;

    // If status is Rejected or Query, set current date and time
    if (this.dataExplanationStatus === 'Rejected' || this.dataExplanationStatus === 'Query') {
      this.dataExplanationFollowUpDate = this.getCurrentDate();
      this.dataExplanationFollowUpTime = this.getCurrentTime();
    }
  }

  // Submit data explanation follow-up
  submitDataExplanationFollowUp(): void {
    // Validate inputs
    if (!this.dataExplanationNotes) {
      alert('Please enter follow-up notes');
      return;
    }

    if (!this.dataExplanationFollowUpDate) {
      alert('Please select a follow-up date');
      return;
    }

    if (!this.dataExplanationFollowUpTime) {
      alert('Please select a follow-up time');
      return;
    }

    // Add to follow-up history
    this.dataExplanationFollowUps.push({
      date: this.dataExplanationFollowUpDate,
      time: this.dataExplanationFollowUpTime,
      notes: this.dataExplanationNotes,
      status: this.dataExplanationStatus,
      timestamp: new Date().getTime() // For sorting
    });

    // Here you would typically save the follow-up data to a database
    // For now, we'll just show an alert
    alert('Follow-up submitted successfully!');

    // Reset the form fields but keep the status
    this.dataExplanationNotes = '';
  }

  // Get the latest N follow-ups for data explanation
  getLatestDataExplanationFollowUps(count: number = 3): Array<{ date: string, time: string, notes: string, status: string, timestamp: number }> {
    if (this.dataExplanationFollowUps.length === 0) {
      return [];
    }

    // Sort by timestamp (descending) and return the first N items
    return [...this.dataExplanationFollowUps]
      .sort((a, b) => b.timestamp - a.timestamp)
      .slice(0, count);
  }

  // Calculate the total for a row in the Sales Plan table
  calculateRowTotal(row: SalesPlanRow): string {
    if (!row) return '0';

    let total = 0;

    // For 'Sale value' row (id: 4), calculate based on other rows
    if (row.id === 4) {
      // For each quarter, multiply flats * area * rate
      const quarters = ['q1', 'q2', 'q3', 'q4', 'q5', 'q6', 'q7', 'q8', 'q9'];

      quarters.forEach(quarter => {
        const flatsRow = this.salesPlanRows.find((r: any) => r.id === 1) as SalesPlanRow;
        const areaRow = this.salesPlanRows.find((r: any) => r.id === 2) as SalesPlanRow;
        const rateRow = this.salesPlanRows.find((r: any) => r.id === 3) as SalesPlanRow;

        if (flatsRow && areaRow && rateRow) {
          const flats = flatsRow[quarter] || 0;
          const area = areaRow[quarter] || 0;
          const rate = rateRow[quarter] || 0;

          // Calculate sale value for this quarter
          const quarterValue = flats * area * rate;
          total += quarterValue;
        }
      });
    } else {
      // For other rows, simply sum the quarter values
      total = (row.q1 || 0) + (row.q2 || 0) + (row.q3 || 0) + (row.q4 || 0) +
              (row.q5 || 0) + (row.q6 || 0) + (row.q7 || 0) + (row.q8 || 0) + (row.q9 || 0);
    }

    // Format the number with commas
    return this.formatNumber(total);
  }

  // Calculate the remaining EMIs for the Debt Details table
  calculateRemainingEMIs(row: DebtDetailsRow): number {
    if (!row) return 0;
    return row.totalEmi - row.emiPaid;
  }

  // Calculate the remaining loan amount for the Debt Details table
  calculateRemainingLoanAmount(row: DebtDetailsRow): string {
    if (!row) return '0';
    return this.formatNumber(row.currentOutstanding);
  }

  // Format number with commas
  formatNumber(num: number): string {
    if (num === undefined || num === null) return '0';
    return num.toLocaleString('en-IN');
  }

  // Format date from YYYY-MM-DD to DD-MMM-YYYY format
  formatDateForDisplay(dateString: string): string {
    if (!dateString) return '';

    // If the date is already in DD-MMM-YYYY format, return it as is
    if (dateString.includes('-') && !dateString.match(/^\d{4}-\d{2}-\d{2}$/)) {
      return dateString;
    }

    try {
      const date = new Date(dateString);
      const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
      const day = date.getDate().toString().padStart(2, '0');
      const month = months[date.getMonth()];
      const year = date.getFullYear();

      return `${day}-${month}-${year}`;
    } catch (error) {
      return dateString; // Return original string if parsing fails
    }
  }

  // Handle row click in Sales MIS & Inventory Summary table
  onSalesMisSummaryRowActivate(_event: any) {
    // Disable popup opening when clicking on table rows
    return;
  }

  // Open Sales MIS & Inventory Summary form
  openSalesMisSummaryForm(rowId?: number) {
    // Create a modal for adding/editing Sales MIS & Inventory Summary entries
    const modalRef = this.modalService.open(GenericModalComponent, {
      size: 'lg',
      backdrop: 'static',
      keyboard: false,
      centered: true
    });

    // Set modal title and form fields
    modalRef.componentInstance.modalTitle = rowId ? 'Edit Sales MIS & Inventory Summary Entry' : 'Add Sales MIS & Inventory Summary Entry';

    // If editing, get the existing data
    let existingData: Partial<SalesMisSummaryRow> = {};
    if (rowId !== undefined) {
      const existingRow = this.salesMisSummaryRows.find(row => row.id === rowId);
      if (existingRow) {
        existingData = { ...existingRow };
      }
    }

    // Create form fields
    const formFields = [
      { name: 'saleableBuiltUpArea', label: 'SALEABLE BUILT-UP AREA', type: 'number', required: true, value: existingData.saleableBuiltUpArea || 0 },
      { name: 'mahareraCarpetAreaSqMt', label: 'MAHARERA CARPET AREA (Sq.Mt.)', type: 'number', required: true, value: existingData.mahareraCarpetAreaSqMt || 0 },
      { name: 'carpetAreaSqFt', label: 'CARPET AREA (Sq.Ft.)', type: 'number', required: true, value: existingData.carpetAreaSqFt || 0 },
      { name: 'numberOfFlats', label: 'NUMBER OF FLATS', type: 'number', required: true, value: existingData.numberOfFlats || 0 },
      { name: 'numberOfShops', label: 'NUMBER OF SHOPS', type: 'number', required: true, value: existingData.numberOfShops || 0 },
      { name: 'numberOfOffices', label: 'NUMBER OF OFFICES', type: 'number', required: true, value: existingData.numberOfOffices || 0 },
      { name: 'refugeUnits', label: 'REFUGE UNITS', type: 'number', required: true, value: existingData.refugeUnits || 0 },
      { name: 'achievedForSoldArea', label: 'ACHIEVED FOR SOLD AREA (AS PER CARPET AREA)', type: 'number', required: true, value: existingData.achievedForSoldArea || 0 },
      { name: 'targetForUnsoldArea', label: 'TARGET FOR UNSOLD AREA (AS PER CARPET AREA)', type: 'number', required: true, value: existingData.targetForUnsoldArea || 0 }
    ];

    modalRef.componentInstance.formFields = formFields;

    // Subscribe to the modal close event to get the result
    modalRef.result.then(
      (result) => {
        if (result) {
          if (rowId !== undefined) {
            // Update existing row
            const index = this.salesMisSummaryRows.findIndex(row => row.id === rowId);
            if (index !== -1) {
              this.salesMisSummaryRows[index] = {
                ...this.salesMisSummaryRows[index],
                ...result
              };
            }
          } else {
            // Add new row
            const newId = this.salesMisSummaryRows.length > 0 ? Math.max(...this.salesMisSummaryRows.map(row => row.id)) + 1 : 1;
            this.salesMisSummaryRows.push({
              id: newId,
              ...result
            });
          }
        }
      },
      (reason) => {
        // Modal was dismissed
        console.log('Modal dismissed with reason:', reason);
      }
    );
  }

  // Handle row click in Sales MIS & Inventory table
  onSalesMisRowActivate(_event: any) {
    // Disable popup opening when clicking on table rows
    return;
  }

  // Open Sales MIS & Inventory form
  openSalesMisForm(rowId?: number) {
    // Create a modal for adding/editing Sales MIS & Inventory entries
    const modalRef = this.modalService.open(GenericModalComponent, {
      size: 'lg',
      backdrop: 'static',
      keyboard: false,
      centered: true
    });

    // Set modal title and form fields
    modalRef.componentInstance.modalTitle = rowId ? 'Edit Sales MIS & Inventory Entry' : 'Add Sales MIS & Inventory Entry';

    // If editing, get the existing data
    let existingData: Partial<SalesMisRow> = {};
    if (rowId !== undefined) {
      const existingRow = this.salesMisRows.find(row => row.id === rowId);
      if (existingRow) {
        existingData = { ...existingRow };
      }
    }

    // Create form fields
    const formFields = [
      { name: 'buildingWingName', label: 'BUILDING NO / WING NAME', type: 'text', required: true, value: existingData['buildingWingName'] || '' },
      { name: 'floorNo', label: 'FLOOR NO', type: 'text', required: true, value: existingData['floorNo'] || '' },
      { name: 'unitNo', label: 'UNIT NO', type: 'text', required: true, value: existingData['unitNo'] || '' },
      { name: 'mahareraCarpetArea', label: 'MAHARERA CARPET AREA', type: 'number', required: true, value: existingData['mahareraCarpetArea'] || 0 },
      { name: 'carpetArea', label: 'CARPET AREA', type: 'number', required: true, value: existingData['carpetArea'] || 0 },
      { name: 'saleableBuiltUpArea', label: 'SALEABLE BUILT UP AREA', type: 'number', required: true, value: existingData['saleableBuiltUpArea'] || 0 },
      { name: 'unitType', label: 'TYPE OF UNIT (COMM / RESI)', type: 'select', options: ['COMM', 'RESI'], required: true, value: existingData['unitType'] || '' },
      { name: 'ownerType', label: 'DEVELOPER / LANDOWNER / TENANT', type: 'select', options: ['DEVELOPER', 'LANDOWNER', 'TENANT'], required: true, value: existingData['ownerType'] || '' },
      { name: 'undividedShareOfLand', label: 'UNDIVIDED SHARE OF LAND', type: 'number', required: false, value: existingData['undividedShareOfLand'] || 0 },
      { name: 'extraCarpetAreaSoldToExistingTenants', label: 'EXTRA CARPET AREA SOLD TO EXISTING TENANTS', type: 'number', required: false, value: existingData['extraCarpetAreaSoldToExistingTenants'] || 0 },
      { name: 'extraSaleableAreaSoldToExistingTenants', label: 'EXTRA SALEABLE AREA SOLD TO EXISTING TENANTS', type: 'number', required: false, value: existingData['extraSaleableAreaSoldToExistingTenants'] || 0 },
      { name: 'saleStatus', label: 'SOLD / UNSOLD / MORTGAGED', type: 'select', options: ['SOLD', 'UNSOLD', 'MORTGAGED'], required: true, value: existingData['saleStatus'] || '' },
      { name: 'buyerName', label: 'NAME OF THE BUYER', type: 'text', required: false, value: existingData['buyerName'] || '' },
      { name: 'contactNo', label: 'CONTACT NO', type: 'text', required: false, value: existingData['contactNo'] || '' },
      { name: 'emailId', label: 'EMAIL ID', type: 'text', required: false, value: existingData['emailId'] || '' },
      { name: 'dateOfBooking', label: 'DATE OF BOOKING', type: 'text', required: false, value: existingData['dateOfBooking'] || '' },
      { name: 'dateOfRegistration', label: 'DATE OF REGISTRATION', type: 'text', required: false, value: existingData['dateOfRegistration'] || '' },
      { name: 'registrationNo', label: 'REGISTRATION / AGREEMENT NO', type: 'text', required: false, value: existingData['registrationNo'] || '' },
      { name: 'basicCost', label: 'BASIC COST', type: 'number', required: false, value: existingData['basicCost'] || 0 },
      { name: 'developmentCostComponents', label: 'DEVELOPMENT COST COMPONENTS', type: 'number', required: false, value: existingData['developmentCostComponents'] || 0 },
      { name: 'totalValueOfUnit', label: 'TOTAL VALUE OF UNIT', type: 'number', required: false, value: existingData['totalValueOfUnit'] || 0 },
      { name: 'demandRaised', label: 'DEMAND RAISED', type: 'number', required: false, value: existingData['demandRaised'] || 0 },
      { name: 'amountReceived', label: 'AMOUNT RECEIVED', type: 'number', required: false, value: existingData['amountReceived'] || 0 },
      { name: 'balance', label: 'BALANCE', type: 'number', required: false, value: existingData['balance'] || 0 },
      { name: 'stageOfConstruction', label: 'STAGE OF CONSTRUCTION', type: 'text', required: false, value: existingData['stageOfConstruction'] || '' },
      { name: 'collections', label: 'COLLECTIONS', type: 'number', required: false, value: existingData['collections'] || 0 },
      { name: 'homeLoanFinancierName', label: 'HOME LOAN FINANCIER\'S NAME', type: 'text', required: false, value: existingData['homeLoanFinancierName'] || '' },
      { name: 'nocIssuedByEarlierFinancier', label: 'NOC ISSUED BY EARLIER FINANCIER IN CASE OF BT TAKE OVER', type: 'text', required: false, value: existingData['nocIssuedByEarlierFinancier'] || '' },
      { name: 'typeOfCustomer', label: 'TYPE OF CUSTOMER', type: 'select', options: ['Salaried', 'Self-employed', 'Business Owner', ''], required: false, value: existingData['typeOfCustomer'] || '' }
    ];

    modalRef.componentInstance.formFields = formFields;

    // Subscribe to the modal close event to get the result
    modalRef.result.then(
      (result) => {
        if (result) {
          if (rowId !== undefined) {
            // Update existing row
            const index = this.salesMisRows.findIndex(row => row.id === rowId);
            if (index !== -1) {
              this.salesMisRows[index] = {
                ...this.salesMisRows[index],
                ...result
              };
            }
          } else {
            // Add new row
            const newId = this.salesMisRows.length > 0 ? Math.max(...this.salesMisRows.map(row => row.id)) + 1 : 1;
            this.salesMisRows.push({
              id: newId,
              ...result
            });
          }
        }
      },
      (reason) => {
        // Modal was dismissed
        console.log('Modal dismissed with reason:', reason);
      }
    );
  }

  // Open Sales Plan modal
  openSalesPlanForm(rowId?: number) {
    // Open the modal with the SalesPlanModalComponent
    const modalRef = this.modalService.open(SalesPlanModalComponent, {
      size: 'lg',
      backdrop: 'static',
      keyboard: false,
      centered: true
    });

    // Pass the rowId to the modal component if provided
    if (rowId !== undefined) {
      modalRef.componentInstance.rowId = rowId;
    }

    // Subscribe to the modal close event to get the result
    modalRef.result.then(
      (result) => {
        // Handle the result when the modal is closed (not dismissed)
        if (result) {
          // Update the sales plan data
          const quarterKey = result.quarter; // e.g., 'q1', 'q2', etc.

          // Update the flats booked row (id: 1)
          const flatsRow = this.salesPlanRows.find(row => row.id === 1);
          if (flatsRow) {
            flatsRow[quarterKey] = result.flatsBooked;
          }

          // Update the average area row (id: 2)
          const areaRow = this.salesPlanRows.find(row => row.id === 2);
          if (areaRow) {
            areaRow[quarterKey] = result.averageArea;
          }

          // Update the rate per sq. ft. row (id: 3)
          const rateRow = this.salesPlanRows.find(row => row.id === 3);
          if (rateRow) {
            rateRow[quarterKey] = result.ratePerSqFt;
          }

          // Update the sale value row (id: 4)
          const saleValueRow = this.salesPlanRows.find(row => row.id === 4);
          if (saleValueRow) {
            saleValueRow[quarterKey] = result.saleValue;
          }
        }
      },
      (reason) => {
        // Modal was dismissed
        console.log('Modal dismissed with reason:', reason);
      }
    );
  }

  // Open Debt Details modal
  openDebtDetailsForm(rowId?: number) {
    // Open the modal with the DebtDetailsModalComponent
    const modalRef = this.modalService.open(DebtDetailsModalComponent, {
      size: 'lg',
      backdrop: 'static',
      keyboard: false,
      centered: true,
      scrollable: true
    });

    // Pass the rowId to the modal component if provided
    if (rowId !== undefined) {
      modalRef.componentInstance.rowId = rowId;
    }

    // Subscribe to the modal close event to get the result
    modalRef.result.then(
      (result) => {
        // Handle the result when the modal is closed (not dismissed)
        if (result) {
          if (rowId !== undefined) {
            // Update existing record
            const index = this.debtDetailsRows.findIndex(row => row.id === rowId);
            if (index !== -1) {
              this.debtDetailsRows[index] = result;
            }
          } else {
            // Add new record
            const newId = this.debtDetailsRows.length > 0
              ? Math.max(...this.debtDetailsRows.map(row => row.id)) + 1
              : 1;

            this.debtDetailsRows.push({
              ...result,
              id: newId
            });
          }
        }
      },
      (reason) => {
        // Modal was dismissed
        console.log('Modal dismissed with reason:', reason);
      }
    );
  }

  // Open Other Business Details modal
  openOtherBusinessForm(rowId?: number) {
    // Open the modal with the OtherBusinessModalComponent
    const modalRef = this.modalService.open(OtherBusinessModalComponent, {
      size: 'lg',
      backdrop: 'static',
      keyboard: false,
      centered: true
    });

    // Pass the rowId to the modal component if provided
    if (rowId !== undefined) {
      modalRef.componentInstance.rowId = rowId;
    }

    // Subscribe to the modal close event to get the result
    modalRef.result.then(
      (result) => {
        // Handle the result when the modal is closed (not dismissed)
        if (result) {
          if (rowId !== undefined) {
            // Update existing record
            const index = this.otherBusinessRows.findIndex(row => row.id === rowId);
            if (index !== -1) {
              this.otherBusinessRows[index] = result;
            }
          } else {
            // Add new record
            const newId = this.otherBusinessRows.length > 0
              ? Math.max(...this.otherBusinessRows.map(row => row.id)) + 1
              : 1;

            this.otherBusinessRows.push({
              ...result,
              id: newId
            });
          }
        }
      },
      (reason) => {
        // Modal was dismissed
        console.log('Modal dismissed with reason:', reason);
      }
    );
  }

  // Open Group Entity modal
  openGroupEntityForm(rowId?: number) {
    // Open the modal with the GroupEntityModalComponent
    const modalRef = this.modalService.open(GroupEntityModalComponent, {
      size: 'lg',
      backdrop: 'static',
      keyboard: false,
      centered: true
    });

    // Pass the rowId to the modal component if provided
    if (rowId !== undefined) {
      modalRef.componentInstance.rowId = rowId;
    }

    // Subscribe to the modal close event to get the result
    modalRef.result.then(
      (result) => {
        // Handle the result when the modal is closed (not dismissed)
        if (result) {
          if (rowId !== undefined) {
            // Update existing record
            const index = this.groupEntitiesRows.findIndex(row => row.id === rowId);
            if (index !== -1) {
              this.groupEntitiesRows[index] = result;
            }
          } else {
            // Add new record
            const newId = this.groupEntitiesRows.length > 0
              ? Math.max(...this.groupEntitiesRows.map(row => row.id)) + 1
              : 1;

            this.groupEntitiesRows.push({
              ...result,
              id: newId
            });
          }
        }
      },
      (reason) => {
        // Modal was dismissed
        console.log('Modal dismissed with reason:', reason);
      }
    );
  }

  // Open Completed Project modal
  openCompletedProjectForm(projectId?: number) {
    // Open the modal with the CompletedProjectModalComponent
    const modalRef = this.modalService.open(CompletedProjectModalComponent, {
      size: 'lg',
      backdrop: 'static',
      keyboard: false,
      centered: true
    });

    // Pass the projectId and projects array to the modal component
    if (projectId !== undefined) {
      modalRef.componentInstance.projectId = projectId;
    }
    modalRef.componentInstance.projects = this.completedProjectRows;

    // Subscribe to the modal close event to get the result
    modalRef.result.then(
      (result) => {
        // Handle the result when the modal is closed (not dismissed)
        if (result) {
          if (projectId !== undefined) {
            // Update existing record
            const index = this.completedProjectRows.findIndex(row => row.id === projectId);
            if (index !== -1) {
              this.completedProjectRows[index] = result;
            }
          } else {
            // Add new record
            const newId = this.completedProjectRows.length > 0
              ? Math.max(...this.completedProjectRows.map(row => row.id)) + 1
              : 1;

            this.completedProjectRows.push({
              ...result,
              id: newId
            });
          }
        }
      },
      (reason) => {
        // Modal was dismissed
        console.log('Modal dismissed with reason:', reason);
      }
    );
  }

  // Open Ongoing Project modal
  openOngoingProjectForm(projectId?: number) {
    // Open the modal with the GenericModalComponent
    const modalRef = this.modalService.open(GenericModalComponent, {
      size: 'lg',
      backdrop: 'static',
      keyboard: false,
      centered: true
    });

    // Set modal title and form fields
    modalRef.componentInstance.modalTitle = projectId ? 'Edit Ongoing Project' : 'Add Ongoing Project';

    // Create form fields based on OngoingProjectRow interface - only include the required fields
    const formFields = [
      { name: 'projectName', label: 'Project Name', type: 'text', required: true },
      { name: 'entityName', label: 'Project Developing Company', type: 'text', required: true },
      { name: 'reraNumber', label: 'MAHARERA NO', type: 'text', required: false },
      { name: 'location', label: 'Location', type: 'text', required: true },
      { name: 'surveyNumbers', label: 'S NO & H NO / CTS NO', type: 'text', required: false },
      { name: 'promotersNames', label: 'Promoters\' Names', type: 'text', required: true },
      { name: 'projectStructure', label: 'ENTIRE PROJECT STRUCTURE', type: 'text', required: false },
      { name: 'constructionApprovals', label: 'CURRENT CONSTRUCTION APPROVALS', type: 'text', required: false },
      { name: 'latestCCDate', label: 'DATE OF LATEST CC', type: 'text', required: false },
      { name: 'projectCategory', label: 'NORMAL LAYOUT / SOC REDEV / SRA REDEV / MHADA REDEV / CESSED REDEV', type: 'select', options: ['NORMAL LAYOUT', 'SOC REDEV', 'SRA REDEV', 'MHADA REDEV', 'CESSED REDEV'], required: false },
      { name: 'ownershipType', label: 'OWNED / JDA / JV', type: 'select', options: ['OWNED', 'JDA', 'JV'], required: false },
      { name: 'plotArea', label: 'LAND / PLOT AREA', type: 'text', required: false },
      { name: 'category', label: 'CATEGORY', type: 'text', required: false },
      { name: 'sharingDetails', label: 'SHARING DETAILS (%)', type: 'text', required: false },
      { name: 'area1', label: 'AREA (SQ FT) 1', type: 'number', required: false },
      { name: 'units1', label: 'UNITS 1', type: 'number', required: false },
      { name: 'area2', label: 'AREA (SQ FT) 2', type: 'number', required: false },
      { name: 'units2', label: 'UNITS 2', type: 'number', required: false },
      { name: 'area3', label: 'AREA (SQ FT) 3', type: 'number', required: false },
      { name: 'units3', label: 'UNITS 3', type: 'number', required: false },
      { name: 'soldArea', label: 'SOLD AREA (SQ FT)', type: 'number', required: false },
      { name: 'unsoldArea', label: 'UNSOLD AREA (SQ FT)', type: 'number', required: false },
      { name: 'soldUnits', label: 'SOLD UNITS', type: 'number', required: false },
      { name: 'unsoldUnits', label: 'UNSOLD UNITS', type: 'number', required: false },
      { name: 'totalEstimatedRevenue', label: 'TOTAL ESTIMATED REVENUE', type: 'number', required: false },
      { name: 'valueOfAreaUnsold', label: 'VALUE OF AREA UNSOLD', type: 'number', required: false },
      { name: 'valueOfAreaSold', label: 'VALUE OF AREA SOLD', type: 'number', required: false },
      { name: 'receiptsFromSoldArea', label: 'RECEIPTS FROM SOLD AREA', type: 'number', required: false },
      { name: 'receivablesFromSoldArea', label: 'RECEIVABLES FROM SOLD AREA', type: 'number', required: false },
      { name: 'totalCost', label: 'TOTAL', type: 'number', required: false },
      { name: 'costIncurred', label: 'COST INCURRED', type: 'number', required: false },
      { name: 'costToBeIncurred', label: 'COST TO BE INCURRED', type: 'number', required: false },
      { name: 'promoterSkinInfusion', label: 'PROMOTER\'S SKIN (INFUSION)', type: 'number', required: false },
      { name: 'profitInCrores', label: 'PROFIT (RS IN CRS)', type: 'number', required: false },
      { name: 'profitPercentOnRevenue', label: 'PROFIT % ON REVENUE', type: 'number', required: false },
      { name: 'surplusInCrores', label: 'SURPLUS (RS IN CRS)', type: 'number', required: false },
      { name: 'rateSold', label: 'RATE (SOLD)', type: 'number', required: false },
      { name: 'rateUnsold', label: 'RATE (UNSOLD)', type: 'number', required: false },
      { name: 'percentCompletion', label: '% COMPLETION', type: 'number', required: false },
      { name: 'percentCollections', label: '% COLLECTIONS', type: 'number', required: false },
      { name: 'existingLoan', label: 'EXISTING LOAN (IF ANY)', type: 'select', options: ['Yes', 'No'], required: false },
      { name: 'bankName', label: 'NAME OF BANK/FI', type: 'text', required: false },
      { name: 'sanctionDate', label: 'SANCTION DATE', type: 'text', required: false },
      { name: 'rateOfInterest', label: 'ROI (%)', type: 'number', required: false },
      { name: 'amountSanctioned', label: 'AMOUNT SANCTIONED', type: 'number', required: false },
      { name: 'amountDisbursed', label: 'AMOUNT DISBURSED', type: 'number', required: false },
      { name: 'currentOutstandingAmount', label: 'CURRENT OUTSTANDING AMOUNT', type: 'number', required: false },
      { name: 'moratPeriod', label: 'MORAT PERIOD', type: 'text', required: false },
      { name: 'repaymentStartingDate', label: 'REPAYMENT STARTING DATE', type: 'text', required: false },
      { name: 'projectStartDate', label: 'START', type: 'text', required: false },
      { name: 'projectLaunchDate', label: 'LAUNCH', type: 'text', required: false },
      { name: 'currentPhysicalStage', label: 'CURRENT PHYSICAL STAGE OF PROJECT (AS ON __/__/____)', type: 'text', required: false },
      { name: 'projectCompletionDate', label: 'COMPLETION', type: 'text', required: false }
    ];

    modalRef.componentInstance.formFields = formFields;

    // Set initial form data if editing
    if (projectId !== undefined) {
      const project = this.ongoingProjectRows.find(p => p.id === projectId);
      if (project) {
        // Convert dates from display format to input format if needed
        const formData = { ...project };
        if (formData.startDate) {
          formData.startDate = this.convertToInputDateFormat(formData.startDate);
        }
        if (formData.expectedEndDate) {
          formData.expectedEndDate = this.convertToInputDateFormat(formData.expectedEndDate);
        }
        modalRef.componentInstance.formData = formData;
      }
    }

    // Subscribe to the modal close event to get the result
    modalRef.result.then(
      (result) => {
        // Handle the result when the modal is closed (not dismissed)
        if (result) {
          // Format dates for display
          if (result.startDate) {
            result.startDate = this.convertToDisplayDateFormat(result.startDate);
          }
          if (result.expectedEndDate) {
            result.expectedEndDate = this.convertToDisplayDateFormat(result.expectedEndDate);
          }

          if (projectId !== undefined) {
            // Update existing record
            const index = this.ongoingProjectRows.findIndex(row => row.id === projectId);
            if (index !== -1) {
              this.ongoingProjectRows[index] = { ...this.ongoingProjectRows[index], ...result };
            }
          } else {
            // Add new record
            const newId = this.ongoingProjectRows.length > 0
              ? Math.max(...this.ongoingProjectRows.map(row => row.id)) + 1
              : 1;

            this.ongoingProjectRows.push({
              ...result,
              id: newId
            });
          }
        }
      },
      (reason) => {
        // Modal was dismissed
        console.log('Modal dismissed with reason:', reason);
      }
    );
  }

  // Handle row activation for ongoing projects table
  onOngoingProjectRowActivate(_event: any) {
    // Disable popup opening when clicking on table rows
    return;
  }

  // Convert date from YYYY-MM-DD to DD-MMM-YYYY format for display
  convertToDisplayDateFormat(dateStr: string): string {
    if (!dateStr) return '';

    try {
      const date = new Date(dateStr);
      const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
      const day = date.getDate().toString().padStart(2, '0');
      const month = months[date.getMonth()];
      const year = date.getFullYear();

      return `${day}-${month}-${year}`;
    } catch (e) {
      return dateStr;
    }
  }

  // Convert date from DD-MMM-YYYY to YYYY-MM-DD format for input
  convertToInputDateFormat(dateStr: string): string {
    if (!dateStr) return '';

    try {
      const parts = dateStr.split('-');
      if (parts.length !== 3) return dateStr;

      const day = parts[0];
      const monthStr = parts[1];
      const year = parts[2];

      const months = {'Jan': 0, 'Feb': 1, 'Mar': 2, 'Apr': 3, 'May': 4, 'Jun': 5,
                      'Jul': 6, 'Aug': 7, 'Sep': 8, 'Oct': 9, 'Nov': 10, 'Dec': 11};

      const month = (months[monthStr as keyof typeof months] + 1).toString().padStart(2, '0');

      return `${year}-${month}-${day}`;
    } catch (e) {
      return dateStr;
    }
  }

  // Handle Sathbara Entry form
  openSathbaraEntryForm(rowId?: number) {
    // Open the modal with the SathbaraEntryModalComponent
    const modalRef = this.modalService.open(SathbaraEntryModalComponent, {
      size: 'lg',
      backdrop: 'static',
      keyboard: false,
      centered: true
    });

    // Pass the rowId to the modal component if provided
    if (rowId !== undefined) {
      modalRef.componentInstance.rowId = rowId;
    }

    // Subscribe to the modal close event to get the result
    modalRef.result.then(
      (result) => {
        // Handle the result when the modal is closed (not dismissed)
        if (result) {
          if (rowId !== undefined) {
            // Update existing record
            const index = this.sathbaraEntryRows.findIndex(row => row.id === rowId);
            if (index !== -1) {
              this.sathbaraEntryRows[index] = result;
            }
          } else {
            // Add new record
            const newId = this.sathbaraEntryRows.length > 0
              ? Math.max(...this.sathbaraEntryRows.map(row => row.id)) + 1
              : 1;

            this.sathbaraEntryRows.push({
              ...result,
              id: newId
            });
          }
        }
      },
      (reason) => {
        // Modal was dismissed
        console.log('Modal dismissed with reason:', reason);
      }
    );
  }

  // Delete Sathbara Entry
  deleteSathbaraEntry(rowId: number) {
    // Confirm deletion
    if (confirm('Are you sure you want to delete this entry?')) {
      // Remove the entry from the array
      this.sathbaraEntryRows = this.sathbaraEntryRows.filter(row => row.id !== rowId);

      alert('Entry deleted successfully!');
    }
  }

  // Check if data explanation has any follow-ups
  hasDataExplanationFollowUps(): boolean {
    return this.dataExplanationFollowUps.length > 0;
  }

  // Handle row click in Completed Projects table
  onCompletedProjectRowActivate(_event: any) {
    // Disable popup opening when clicking on table rows
    return;
  }

  // Handle row click in Unsold Stock table
  onUnsoldStockRowActivate(_event: any) {
    // Disable popup opening when clicking on table rows
    return;
  }

  // Open Unsold Stock modal
  openUnsoldStockForm(stockId?: number) {
    // Import the UnsoldStockModalComponent dynamically
    import('./unsold-stock-modal/unsold-stock-modal.component').then(({ UnsoldStockModalComponent }) => {
      // Open the modal with the UnsoldStockModalComponent
      const modalRef = this.modalService.open(UnsoldStockModalComponent, {
        size: 'lg',
        backdrop: 'static',
        keyboard: false,
        centered: true
      });

      // Pass the stockId and stocks array to the modal component
      if (stockId !== undefined) {
        modalRef.componentInstance.stockId = stockId;
      }
      modalRef.componentInstance.stocks = this.unsoldStockRows;

      // Subscribe to the modal close event to get the result
      modalRef.result.then(
        (result) => {
          // Handle the result when the modal is closed (not dismissed)
          if (result) {
            if (stockId !== undefined) {
              // Update existing record
              const index = this.unsoldStockRows.findIndex(row => row.id === stockId);
              if (index !== -1) {
                this.unsoldStockRows[index] = result;
              }
            } else {
              // Add new record
              const newId = this.unsoldStockRows.length > 0
                ? Math.max(...this.unsoldStockRows.map(row => row.id)) + 1
                : 1;

              this.unsoldStockRows.push({
                ...result,
                id: newId
              });
            }
          }
        },
        (reason) => {
          // Modal was dismissed
          console.log('Modal dismissed with reason:', reason);
        }
      );
    });
  }

  // Handle row click in Leased Properties table
  onLeasedPropertyRowActivate(_event: any) {
    // Disable popup opening when clicking on table rows
    return;
  }

  // Open Leased Property modal
  openLeasedPropertyForm(propertyId?: number) {
    // Import the LeasedPropertyModalComponent dynamically
    import('./leased-property-modal/leased-property-modal.component').then(({ LeasedPropertyModalComponent }) => {
      // Open the modal with the LeasedPropertyModalComponent
      const modalRef = this.modalService.open(LeasedPropertyModalComponent, {
        size: 'lg',
        backdrop: 'static',
        keyboard: false,
        centered: true
      });

      // Pass the propertyId and properties array to the modal component
      if (propertyId !== undefined) {
        modalRef.componentInstance.propertyId = propertyId;
      }
      modalRef.componentInstance.properties = this.leasedPropertyRows;

      // Subscribe to the modal close event to get the result
      modalRef.result.then(
        (result) => {
          // Handle the result when the modal is closed (not dismissed)
          if (result) {
            if (propertyId !== undefined) {
              // Update existing record
              const index = this.leasedPropertyRows.findIndex(row => row.id === propertyId);
              if (index !== -1) {
                this.leasedPropertyRows[index] = result;
              }
            } else {
              // Add new record
              const newId = this.leasedPropertyRows.length > 0
                ? Math.max(...this.leasedPropertyRows.map(row => row.id)) + 1
                : 1;

              this.leasedPropertyRows.push({
                ...result,
                id: newId
              });
            }
          }
        },
        (reason) => {
          // Modal was dismissed
          console.log('Modal dismissed with reason:', reason);
        }
      );
    });
  }

  // Handle row click in Compiled Cost table
  onCompiledCostRowActivate(_event: any) {
    // Disable popup opening when clicking on table rows
    return;
  }

  // Open Compiled Cost modal
  openCompiledCostForm(costId?: number) {
    // Create a modal for editing the compiled cost
    const modalRef = this.modalService.open(GenericModalComponent, {
      size: 'lg',
      backdrop: 'static',
      keyboard: false,
      centered: true
    });

    // Set the modal title and form fields
    modalRef.componentInstance.title = costId ? 'Edit Cost Item' : 'Add New Cost Item';

    // Create form fields
    const formFields = [
      { name: 'particulars', label: 'Particulars', type: 'text', required: true, value: '' },
      { name: 'totalCost', label: 'Total Cost (₹)', type: 'number', required: true, value: 0 },
      { name: 'costIncurred', label: 'Cost Incurred Till ' + this.currentDate + ' (₹)', type: 'number', required: true, value: 0 },
      { name: 'costToBeIncurred', label: 'Cost To Be Incurred (₹)', type: 'number', required: true, value: 0 },
      { name: 'percentage', label: 'Percentage (%)', type: 'number', required: true, value: 0 }
    ];

    // If editing, populate with existing data
    if (costId !== undefined) {
      const costItem = this.compiledCostRows.find(row => row.id === costId);
      if (costItem) {
        formFields.forEach(field => {
          field.value = costItem[field.name];
        });
      }
    }

    modalRef.componentInstance.formFields = formFields;

    // Subscribe to the modal close event to get the result
    modalRef.result.then(
      (result) => {
        // Handle the result when the modal is closed (not dismissed)
        if (result) {
          if (costId !== undefined) {
            // Update existing record
            const index = this.compiledCostRows.findIndex(row => row.id === costId);
            if (index !== -1) {
              this.compiledCostRows[index] = {
                ...this.compiledCostRows[index],
                ...result
              };
            }
          } else {
            // Add new record
            const newId = this.compiledCostRows.length > 0
              ? Math.max(...this.compiledCostRows.map(row => row.id)) + 1
              : 1;

            this.compiledCostRows.push({
              id: newId,
              ...result
            });
          }

          // Recalculate percentages if needed
          this.recalculatePercentages();
        }
      },
      (reason) => {
        // Modal was dismissed
        console.log('Modal dismissed with reason:', reason);
      }
    );
  }

  // Recalculate percentages based on total amount
  recalculatePercentages() {
    const totalAmount = this.getTotalCost();
    if (totalAmount > 0) {
      this.compiledCostRows.forEach(row => {
        row.percentage = parseFloat(((row.totalCost / totalAmount) * 100).toFixed(2));
      });
    }
  }

  // Get total cost for compiled cost table
  getTotalCost(): number {
    return this.compiledCostRows.reduce((sum, row) => sum + row.totalCost, 0);
  }

  // Get total cost incurred for compiled cost table
  getTotalCostIncurred(): number {
    return this.compiledCostRows.reduce((sum, row) => sum + row.costIncurred, 0);
  }

  // Get total cost to be incurred for compiled cost table
  getTotalCostToBeIncurred(): number {
    return this.compiledCostRows.reduce((sum, row) => sum + row.costToBeIncurred, 0);
  }

  // Handle row click in Fund Infusion table
  onFundInfusionRowActivate(_event: any) {
    // Disable popup opening when clicking on table rows
    return;
  }

  // Open Fund Infusion modal
  openFundInfusionForm(fundId?: number) {
    // Create a modal for editing the fund infusion
    const modalRef = this.modalService.open(GenericModalComponent, {
      size: 'lg',
      backdrop: 'static',
      keyboard: false,
      centered: true
    });

    // Set the modal title and form fields
    modalRef.componentInstance.title = fundId ? 'Edit Funding Source' : 'Add New Funding Source';

    // Create form fields
    const formFields = [
      {
        name: 'particulars',
        label: 'Particulars',
        type: 'select',
        required: true,
        value: '',
        options: [
          { value: 'Bookings / Advances', label: 'Bookings / Advances' },
          { value: 'Project Finance', label: 'Project Finance' },
          { value: 'Promoter\'s Contribution', label: 'Promoter\'s Contribution' },
          { value: 'Unsecured Loans', label: 'Unsecured Loans' }
        ]
      },
      { name: 'total', label: 'Total (₹)', type: 'number', required: true, value: 0 },
      { name: 'infusion', label: 'Infusion (₹)', type: 'number', required: true, value: 0 },
      { name: 'infusionTillDate', label: 'Infusion Till ' + this.currentDate + ' (₹)', type: 'number', required: true, value: 0 },
      { name: 'toBeInfused', label: 'To Be Infused (₹)', type: 'number', required: true, value: 0 },
      { name: 'percentage', label: 'Percentage (%)', type: 'number', required: true, value: 0 }
    ];

    // If editing, populate with existing data
    if (fundId !== undefined) {
      const fundItem = this.fundInfusionRows.find(row => row.id === fundId);
      if (fundItem) {
        formFields.forEach(field => {
          field.value = fundItem[field.name];
        });
      }
    }

    modalRef.componentInstance.formFields = formFields;

    // Subscribe to the modal close event to get the result
    modalRef.result.then(
      (result) => {
        // Handle the result when the modal is closed (not dismissed)
        if (result) {
          if (fundId !== undefined) {
            // Update existing record
            const index = this.fundInfusionRows.findIndex(row => row.id === fundId);
            if (index !== -1) {
              this.fundInfusionRows[index] = {
                ...this.fundInfusionRows[index],
                ...result
              };
            }
          } else {
            // Add new record
            const newId = this.fundInfusionRows.length > 0
              ? Math.max(...this.fundInfusionRows.map(row => row.id)) + 1
              : 1;

            this.fundInfusionRows.push({
              id: newId,
              ...result
            });
          }

          // Recalculate percentages if needed
          this.recalculateFundPercentages();
        }
      },
      (reason) => {
        // Modal was dismissed
        console.log('Modal dismissed with reason:', reason);
      }
    );
  }

  // Recalculate percentages based on total amount for fund infusion
  recalculateFundPercentages() {
    const totalAmount = this.getTotalFund();
    if (totalAmount > 0) {
      this.fundInfusionRows.forEach(row => {
        row.percentage = parseFloat(((row.total / totalAmount) * 100).toFixed(2));
      });
    }
  }

  // Get total fund for fund infusion table
  getTotalFund(): number {
    return this.fundInfusionRows.reduce((sum, row) => sum + row.total, 0);
  }

  // Get total infusion for fund infusion table
  getTotalInfusion(): number {
    return this.fundInfusionRows.reduce((sum, row) => sum + row.infusion, 0);
  }

  // Get total infusion till date for fund infusion table
  getTotalInfusionTillDate(): number {
    return this.fundInfusionRows.reduce((sum, row) => sum + row.infusionTillDate, 0);
  }

  // Get total to be infused for fund infusion table
  getTotalToBeInfused(): number {
    return this.fundInfusionRows.reduce((sum, row) => sum + row.toBeInfused, 0);
  }

  // Handle row click in Land Bank table
  onLandBankRowActivate(_event: any) {
    // Disable popup opening when clicking on table rows
    return;
  }

  // Open Land Bank modal
  openLandBankForm(landId?: number) {
    // Open the modal with the LandBankModalComponent
    const modalRef = this.modalService.open(LandBankModalComponent, {
      size: 'xl',
      backdrop: 'static',
      keyboard: false,
      centered: true
    });

    // Pass the landId to the modal component if provided
    if (landId !== undefined) {
      modalRef.componentInstance.landId = landId;
    }

    // Subscribe to the modal close event to get the result
    modalRef.result.then(
      (result) => {
        // Handle the result when the modal is closed (not dismissed)
        if (result) {
          if (landId !== undefined) {
            // Update existing record
            const index = this.landBankRows.findIndex(row => row.id === landId);
            if (index !== -1) {
              this.landBankRows[index] = {
                ...this.landBankRows[index],
                ...result
              };
            }
          } else {
            // Add new record
            const newId = this.landBankRows.length > 0
              ? Math.max(...this.landBankRows.map(row => row.id)) + 1
              : 1;

            this.landBankRows.push({
              id: newId,
              ...result
            });
          }
        }
      },
      (reason) => {
        // Modal was dismissed
        console.log('Modal dismissed with reason:', reason);
      }
    );
  }

  // Get total residential area
  getTotalResidentialArea(): number {
    return this.landBankRows.reduce((sum, row) => sum + row.residentialArea, 0);
  }

  // Get total commercial area
  getTotalCommercialArea(): number {
    return this.landBankRows.reduce((sum, row) => sum + row.commercialArea, 0);
  }

  // Get total mixed area
  getTotalMixedArea(): number {
    return this.landBankRows.reduce((sum, row) => sum + row.mixedArea, 0);
  }

  // Get total residential units
  getTotalResidentialUnits(): number {
    return this.landBankRows.reduce((sum, row) => sum + row.residentialUnits, 0);
  }

  // Get total commercial units
  getTotalCommercialUnits(): number {
    return this.landBankRows.reduce((sum, row) => sum + row.commercialUnits, 0);
  }

  // Get total mixed units
  getTotalMixedUnits(): number {
    return this.landBankRows.reduce((sum, row) => sum + row.mixedUnits, 0);
  }

  // Handle row click in Land Bank Details table
  onLandBankDetailRowActivate(_event: any) {
    // Disable popup opening when clicking on table rows
    return;
  }

  // Open Land Bank Details modal
  openLandBankDetailForm(landId?: number) {
    // Open the modal with the LandBankDetailModalComponent
    const modalRef = this.modalService.open(LandBankDetailModalComponent, {
      size: 'xl',
      backdrop: 'static',
      keyboard: false,
      centered: true
    });

    // Pass the landId to the modal component if provided
    if (landId !== undefined) {
      modalRef.componentInstance.landId = landId;
    }

    // Subscribe to the modal close event to get the result
    modalRef.result.then(
      (result) => {
        // Handle the result when the modal is closed (not dismissed)
        if (result) {
          if (landId !== undefined) {
            // Update existing record
            const index = this.landBankDetailRows.findIndex(row => row.id === landId);
            if (index !== -1) {
              this.landBankDetailRows[index] = {
                ...this.landBankDetailRows[index],
                ...result
              };
            }
          } else {
            // Add new record
            const newId = this.landBankDetailRows.length > 0
              ? Math.max(...this.landBankDetailRows.map(row => row.id)) + 1
              : 1;

            this.landBankDetailRows.push({
              id: newId,
              ...result
            });
          }
        }
      },
      (reason) => {
        // Modal was dismissed
        console.log('Modal dismissed with reason:', reason);
      }
    );
  }

  // Format plot area with unit
  formatPlotArea(area: number, unit: string): string {
    return `${area.toLocaleString()} ${unit}`;
  }

  // Get total plot area by unit
  getTotalPlotArea(unit: string): number {
    return this.landBankDetailRows
      .filter(row => row.plotAreaUnit === unit)
      .reduce((sum, row) => sum + row.plotArea, 0);
  }

  // Get total purchase value
  getTotalPurchaseValue(): number {
    return this.landBankDetailRows.reduce((sum, row) => sum + row.purchaseValue, 0);
  }

  // Get total market value
  getTotalMarketValue(): number {
    return this.landBankDetailRows.reduce((sum, row) => sum + row.marketValue, 0);
  }

  // Handle row click in Project Land Details table
  onProjectLandDetailRowActivate(_event: any) {
    // Disable popup opening when clicking on table rows
    return;
  }

  // Open Project Land Details modal
  openProjectLandDetailForm(landId?: number) {
    // Open the modal with the ProjectLandModalComponent
    const modalRef = this.modalService.open(ProjectLandModalComponent, {
      size: 'lg',
      backdrop: 'static',
      keyboard: false,
      centered: true
    });

    // Pass the landId to the modal component if provided
    if (landId !== undefined) {
      modalRef.componentInstance.landId = landId;
    }

    // Subscribe to the modal close event to get the result
    modalRef.result.then(
      (result) => {
        // Handle the result when the modal is closed (not dismissed)
        if (result) {
          if (landId !== undefined) {
            // Update existing record
            const index = this.projectLandDetailRows.findIndex(row => row.id === landId);
            if (index !== -1) {
              this.projectLandDetailRows[index] = {
                ...this.projectLandDetailRows[index],
                ...result
              };
            }
          } else {
            // Add new record
            const newId = this.projectLandDetailRows.length > 0
              ? Math.max(...this.projectLandDetailRows.map(row => row.id)) + 1
              : 1;

            this.projectLandDetailRows.push({
              id: newId,
              ...result
            });
          }
        }
      },
      (reason) => {
        // Modal was dismissed
        console.log('Modal dismissed with reason:', reason);
      }
    );
  }

  // Format area in square meters
  formatAreaSqMtrs(area: number): string {
    return `${area.toLocaleString()} Sq Mtrs`;
  }

  // Format area in Guntha/Acre
  formatAreaGunthaAcre(area: number, unit: string): string {
    return `${area.toLocaleString()} ${unit}`;
  }

  // Get total area in square meters
  getTotalAreaSqMtrs(): number {
    return this.projectLandDetailRows.reduce((sum, row) => sum + row.areaSqMtrs, 0);
  }

  // Get total area by unit (Guntha/Acre)
  getTotalAreaByUnit(unit: string): number {
    return this.projectLandDetailRows
      .filter(row => row.areaUnit === unit)
      .reduce((sum, row) => sum + row.areaGunthaAcre, 0);
  }



  // Get current page items
  get tableItems(): OpsTeamData[] {
    return this.filteredOpsTeam
      .slice((this.page - 1) * this.pageSize, (this.page - 1) * this.pageSize + this.pageSize);
  }

  // Refresh ops team data when pagination changes
  refreshOpsTeam() {
    // This method will be called when the page changes
    // The tableItems getter will automatically update with the new page
  }

  // Handle sorting
  onSort({column, direction}: SortEvent) {
    // Reset other headers
    this.headers.forEach(header => {
      if (header.sortable !== column) {
        header.direction = '';
      }
    });

    // Sort the data
    if (direction === '' || column === '') {
      this.filteredOpsTeam = [...this.opsTeamData];
    } else {
      this.filteredOpsTeam = [...this.opsTeamData].sort((a, b) => {
        const res = compare(a[column], b[column]);
        return direction === 'asc' ? res : -res;
      });
    }
  }

  // Show details form
  showDetails(id: number = 0) {
    if (id === 0) {
      // Navigate to add-details route
      this.router.navigate(['/bucket/add-details']);
    } else {
      // Navigate to edit route with ID
      this.router.navigate(['/bucket/edit', id]);
    }
  }

  // Back to list view
  backToList() {
    // Navigate back to bucket list
    this.router.navigate(['/bucket']);
  }

  // Get status badge class
  getStatusBadgeClass(status: string): string {
    switch (status.toLowerCase()) {
      case 'completed':
        return 'bg-success';
      case 'in progress':
        return 'bg-warning';
      case 'pending':
        return 'bg-secondary';
      case 'approved':
        return 'bg-success';
      case 'rejected':
        return 'bg-danger';
      case 'query':
        return 'bg-info';
      case 'received':
        return 'bg-success';
      case 'not received':
        return 'bg-danger';
      case 'yes':
        return 'bg-success';
      case 'no':
        return 'bg-danger';
      default:
        return 'bg-light';
    }
  }

  // Quill editor event handlers
  onSelectionChanged = (event: SelectionChange) => {
    if(event.oldRange == null) {
      this.onFocus();
    }
    if(event.range == null) {
      this.onBlur();
    }
  }

  onContentChanged = (event: ContentChange) => {
    // console.log(event.html);
  }

  onFocus = () => {
    console.log("On Focus");
  }

  onBlur = () => {
    console.log("Blurred");
  }

  // Filter documents based on search term
  filterDocuments() {
    if (!this.documentSearchTerm || this.documentSearchTerm.trim() === '') {
      // If search term is empty, show all documents
      this.filteredDocuments = [...this.documents];
    } else {
      // Filter documents based on search term
      const term = this.documentSearchTerm.toLowerCase().trim();
      this.filteredDocuments = this.documents.filter(doc =>
        doc.name.toLowerCase().includes(term) ||
        (doc.status && doc.status.toLowerCase().includes(term))
      );
    }
  }


}